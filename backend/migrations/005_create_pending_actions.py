#!/usr/bin/env python3
"""
创建 pending_actions 表的迁移脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.core.database import get_async_session
from sqlalchemy import text


async def create_pending_actions_table():
    """创建 pending_actions 表"""
    session = await get_async_session()
    
    try:
        # 检查表是否已存在
        result = await session.execute(
            text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'pending_actions'")
        )
        existing_tables = result.fetchall()
        
        if existing_tables:
            print("✅ pending_actions 表已存在")
            return
        
        # 创建 pending_actions 表
        create_table_sql = """
        CREATE TABLE pending_actions (
            id SERIAL PRIMARY KEY,
            task_id VARCHAR(100) NOT NULL,
            user_id UUID NOT NULL,
            action_type VARCHAR(50) NOT NULL,
            details JSONB NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
            response JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
            resolved_at TIMESTAMP WITH TIME ZONE,
            
            -- 外键约束
            CONSTRAINT fk_pending_actions_user_id FOREIGN KEY (user_id) REFERENCES users(id),
            
            -- 检查约束
            CONSTRAINT valid_action_status CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED', 'EXPIRED')),
            CONSTRAINT valid_action_type CHECK (action_type IN ('USER_CONFIRMATION', 'RISK_OVERRIDE', 'MANUAL_INTERVENTION')),
            CONSTRAINT valid_expiry_time CHECK (expires_at > created_at),
            CONSTRAINT valid_resolve_time CHECK (resolved_at IS NULL OR resolved_at >= created_at)
        );
        """
        
        await session.execute(text(create_table_sql))
        
        # 创建索引
        indexes_sql = [
            "CREATE INDEX idx_pending_actions_user_status ON pending_actions (user_id, status);",
            "CREATE INDEX idx_pending_actions_task_id ON pending_actions (task_id);",
            "CREATE INDEX idx_pending_actions_expires_at ON pending_actions (expires_at);",
            "CREATE INDEX idx_pending_actions_created_at ON pending_actions (created_at);",
            "CREATE INDEX idx_pending_actions_details_gin ON pending_actions USING gin (details);",
            "CREATE INDEX idx_pending_actions_response_gin ON pending_actions USING gin (response);"
        ]
        
        for index_sql in indexes_sql:
            await session.execute(text(index_sql))
        
        await session.commit()
        print("✅ pending_actions 表创建成功")
        print("✅ 索引创建成功")
        
    except Exception as e:
        await session.rollback()
        print(f"❌ 创建 pending_actions 表失败: {e}")
        raise
    finally:
        await session.close()


if __name__ == "__main__":
    asyncio.run(create_pending_actions_table())