<template>
  <div class="generic-card-content">
    <!-- 固定头部区域 -->
    <div class="generic-card-header">
      <!-- 消息头部 -->
      <div class="generic-header">
        <div class="generic-author-info">
          <div class="generic-author-line">
            <v-icon size="16" class="mr-2" :color="getPlatformColor(signal.platform)">
              {{ getPlatformIcon(signal.platform) }}
            </v-icon>
            <span class="generic-author-name">{{ signal.author_name || 'Unknown User' }}</span>
            <v-spacer />
            <span class="generic-timestamp">{{ formatTimestamp(signal.created_at) }}</span>
          </div>
          <div v-if="signal.channel_name" class="generic-channel-info">
            <v-icon size="12" class="mr-1">mdi-pound</v-icon>
            <span class="generic-channel-name">{{ signal.channel_name }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 可滚动内容区域 -->
    <div class="generic-card-scrollable">
      <!-- 消息内容 -->
      <div class="generic-content">
      <!-- 文本内容 -->
      <div v-if="signal.content" class="generic-text">
        {{ truncateContent(signal.content, 150) }}
      </div>
      
      <!-- 元数据指示器 -->
      <div v-if="hasMetadata" class="generic-metadata-indicators">
        <!-- Discord特殊内容 -->
        <template v-if="signal.platform === 'discord'">
          <v-chip v-if="hasEmbeds" size="x-small" color="indigo" variant="outlined" class="mr-1">
            <v-icon left size="x-small">mdi-card-text</v-icon>
            {{ embedsCount }} 个嵌入
          </v-chip>
          <v-chip v-if="hasAttachments" size="x-small" color="blue" variant="outlined" class="mr-1">
            <v-icon left size="x-small">mdi-attachment</v-icon>
            {{ attachmentsCount }} 个附件
          </v-chip>
          <v-chip v-if="hasReactions" size="x-small" color="orange" variant="outlined" class="mr-1">
            <v-icon left size="x-small">mdi-emoticon</v-icon>
            {{ reactionsCount }} 个反应
          </v-chip>
        </template>
        
        <!-- Telegram特殊内容 -->
        <template v-else-if="signal.platform === 'telegram'">
          <v-chip v-if="hasMedia" size="x-small" color="blue" variant="outlined" class="mr-1">
            <v-icon left size="x-small">mdi-image</v-icon>
            媒体文件
          </v-chip>
          <v-chip v-if="hasForward" size="x-small" color="green" variant="outlined" class="mr-1">
            <v-icon left size="x-small">mdi-share</v-icon>
            转发消息
          </v-chip>
        </template>
      </div>
    </div>

    <!-- 状态标签 -->
    <div class="generic-status-tags">
      <!-- 平台标签 -->
      <v-chip
        size="x-small"
        :color="getPlatformColor(signal.platform)"
        variant="outlined"
        class="status-tag"
      >
        <v-icon left size="x-small">{{ getPlatformIcon(signal.platform) }}</v-icon>
        {{ getPlatformName(signal.platform) }}
      </v-chip>

      <!-- AI解析状态 -->
      <v-chip
        size="x-small"
        :color="getAIStatusColor(signal.ai_parse_status)"
        variant="outlined"
        class="status-tag"
      >
        <v-icon left size="x-small">{{ getAIStatusIcon(signal.ai_parse_status) }}</v-icon>
        {{ getAIStatusText(signal.ai_parse_status) }}
      </v-chip>

      <!-- 消息类型 -->
      <v-chip
        size="x-small"
        :color="getMessageTypeColor(signal.message_type_ai)"
        variant="outlined"
        class="status-tag"
      >
        <v-icon left size="x-small">{{ getMessageTypeIcon(signal.message_type_ai) }}</v-icon>
        {{ getMessageTypeText(signal.message_type_ai) }}
      </v-chip>

      <!-- 处理状态 -->
      <v-chip
        size="x-small"
        :color="signal.is_processed ? 'success' : 'warning'"
        variant="outlined"
        class="status-tag"
      >
        <v-icon left size="x-small">
          {{ signal.is_processed ? 'mdi-check-circle' : 'mdi-clock-outline' }}
        </v-icon>
        {{ signal.is_processed ? '已处理' : '未处理' }}
      </v-chip>
      </div>

      <!-- 置信度进度条 -->
      <div v-if="signal.confidence !== null" class="generic-confidence">
        <div class="confidence-header">
          <span class="confidence-label">置信度</span>
          <span class="confidence-value">{{ (signal.confidence * 100).toFixed(0) }}%</span>
        </div>
        <v-progress-linear
          :model-value="signal.confidence * 100"
          :color="getConfidenceColor(signal.confidence)"
          height="4"
          rounded
        />
      </div>

      <!-- 统计信息 -->
      <div class="generic-stats">
        <div class="generic-stats-item">
          <v-icon size="12" class="mr-1">mdi-clock-outline</v-icon>
          <span class="generic-stats-text">{{ formatRelativeTime(signal.created_at) }}</span>
        </div>

        <div v-if="signal.llm_service" class="generic-stats-item">
          <v-icon size="12" class="mr-1">{{ getLLMServiceIcon(signal.llm_service) }}</v-icon>
          <span class="generic-stats-text">{{ getLLMServiceText(signal.llm_service) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  signal: {
    type: Object,
    required: true
  }
})

// 计算属性
const hasMetadata = computed(() => {
  return props.signal.metadata && Object.keys(props.signal.metadata).length > 0
})

const hasEmbeds = computed(() => {
  return props.signal.platform === 'discord' &&
         props.signal.metadata?.discord?.embeds?.length > 0
})

const embedsCount = computed(() => {
  return props.signal.metadata?.discord?.embeds?.length || 0
})

const hasAttachments = computed(() => {
  return props.signal.platform === 'discord' &&
         props.signal.metadata?.discord?.attachments?.length > 0
})

const attachmentsCount = computed(() => {
  return props.signal.metadata?.discord?.attachments?.length || 0
})

const hasReactions = computed(() => {
  return props.signal.platform === 'discord' &&
         props.signal.metadata?.discord?.reactions?.length > 0
})

const reactionsCount = computed(() => {
  return props.signal.metadata?.discord?.reactions?.length || 0
})

const hasMedia = computed(() => {
  return props.signal.platform === 'telegram' &&
         props.signal.metadata?.telegram?.has_media
})

const hasForward = computed(() => {
  return props.signal.platform === 'telegram' &&
         props.signal.metadata?.telegram?.forward_from
})

// 方法
const formatTimestamp = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatRelativeTime = (timestamp) => {
  const now = new Date()
  const date = new Date(timestamp)
  const diffInSeconds = Math.floor((now - date) / 1000)

  if (diffInSeconds < 60) {
    return '刚刚'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes}分钟前`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours}小时前`
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

const truncateContent = (content, maxLength) => {
  if (!content) return ''
  if (content.length <= maxLength) return content
  return content.substring(0, maxLength) + '...'
}

const getPlatformColor = (platform) => {
  const colors = {
    discord: 'indigo',
    telegram: 'blue',
    manual: 'green'
  }
  return colors[platform] || 'default'
}

const getPlatformIcon = (platform) => {
  const icons = {
    discord: 'mdi-discord',
    telegram: 'mdi-telegram',
    manual: 'mdi-pencil'
  }
  return icons[platform] || 'mdi-help-circle'
}

const getPlatformName = (platform) => {
  const names = {
    discord: 'Discord',
    telegram: 'Telegram',
    manual: '手动'
  }
  return names[platform] || platform
}

const getAIStatusColor = (status) => {
  const colors = {
    pending: 'warning',
    processing: 'info',
    completed: 'success',
    failed: 'error'
  }
  return colors[status] || 'default'
}

const getAIStatusIcon = (status) => {
  const icons = {
    pending: 'mdi-clock-outline',
    processing: 'mdi-loading',
    completed: 'mdi-check-circle',
    failed: 'mdi-alert-circle'
  }
  return icons[status] || 'mdi-help-circle'
}

const getAIStatusText = (status) => {
  const texts = {
    pending: '待解析',
    processing: '解析中',
    completed: '已解析',
    failed: '解析失败'
  }
  return texts[status] || status
}

const getMessageTypeColor = (type) => {
  const colors = {
    signal: 'success',
    noise: 'error',
    unknown: 'warning'
  }
  return colors[type] || 'default'
}

const getMessageTypeIcon = (type) => {
  const icons = {
    signal: 'mdi-signal',
    noise: 'mdi-signal-off',
    unknown: 'mdi-help-circle'
  }
  return icons[type] || 'mdi-help-circle'
}

const getMessageTypeText = (type) => {
  const texts = {
    signal: '信号',
    noise: '噪音',
    unknown: '未知'
  }
  return texts[type] || type
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 0.8) return 'success'
  if (confidence >= 0.6) return 'warning'
  if (confidence >= 0.4) return 'orange'
  return 'error'
}

const getLLMServiceIcon = (service) => {
  const icons = {
    openai: 'mdi-robot',
    claude: 'mdi-brain',
    gemini: 'mdi-google'
  }
  return icons[service] || 'mdi-robot'
}

const getLLMServiceText = (service) => {
  const texts = {
    openai: 'OpenAI',
    claude: 'Claude',
    gemini: 'Gemini'
  }
  return texts[service] || service
}
</script>

<style scoped>
/* 通用卡片内容样式 - 固定高度和滚动 */
.generic-card-content {
  background: rgb(var(--v-theme-surface));
  border-radius: 8px;
  border-left: 4px solid rgb(var(--v-theme-primary));
  height: 220px; /* 固定高度 220px */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 卡片头部 - 固定不滚动 */
.generic-card-header {
  flex-shrink: 0;
  padding: 12px 12px 8px 12px;
  border-bottom: 1px solid rgb(var(--v-theme-outline-variant));
}

/* 可滚动内容区域 */
.generic-card-scrollable {
  flex: 1;
  overflow-y: auto;
  padding: 8px 12px 12px 12px;
}

/* 通用风格滚动条 - 优化版本 */
.generic-card-scrollable {
  /* 滚动条容器优化 */
  scrollbar-width: thin; /* Firefox 支持 */
  scrollbar-color: rgba(128, 128, 128, 0.4) transparent; /* Firefox 滚动条颜色 */
}

.generic-card-scrollable::-webkit-scrollbar {
  width: 8px; /* 与Discord主题保持一致的精细宽度 */
}

.generic-card-scrollable::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 8px; /* 与卡片圆角保持一致 */
}

.generic-card-scrollable::-webkit-scrollbar-thumb {
  background: rgba(128, 128, 128, 0.3); /* 默认状态更加低调 */
  border-radius: 8px; /* 与卡片设计语言一致 */
  border: 2px solid transparent;
  background-clip: content-box;
  min-height: 32px; /* 减少最小高度 */
  transition: all 0.2s ease; /* 添加平滑过渡 */
}

.generic-card-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(128, 128, 128, 0.5); /* hover时增加可见度 */
  transform: scaleX(1.2); /* 轻微放大效果 */
}

.generic-card-scrollable::-webkit-scrollbar-thumb:active {
  background: rgb(var(--v-theme-primary)); /* active时使用主色调 */
  opacity: 0.8;
}

/* 深色主题优化 */
.v-theme--dark .generic-card-scrollable::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15); /* 深色主题使用白色半透明 */
}

.v-theme--dark .generic-card-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.25);
}

.v-theme--dark .generic-card-scrollable::-webkit-scrollbar-thumb:active {
  background: rgba(255, 255, 255, 0.35);
}

/* 浅色主题优化 */
.v-theme--light .generic-card-scrollable::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15); /* 浅色主题使用黑色半透明 */
}

.v-theme--light .generic-card-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.25);
}

.v-theme--light .generic-card-scrollable::-webkit-scrollbar-thumb:active {
  background: rgba(0, 0, 0, 0.35);
}

/* 消息头部 */
.generic-header {
  /* 移除margin-bottom，因为现在在固定头部区域内 */
}

.generic-author-line {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.generic-author-name {
  font-weight: 600;
  font-size: 14px;
  color: rgb(var(--v-theme-on-surface));
}

.generic-timestamp {
  font-size: 11px;
  color: rgb(var(--v-theme-outline));
  font-weight: 500;
}

.generic-channel-info {
  display: flex;
  align-items: center;
  font-size: 11px;
  color: rgb(var(--v-theme-on-surface-variant));
}

.generic-channel-name {
  color: rgb(var(--v-theme-on-surface));
  font-weight: 500;
}

/* 消息内容 */
.generic-content {
  margin-bottom: 8px;
}

.generic-text {
  font-size: 14px;
  line-height: 1.4;
  color: rgb(var(--v-theme-on-surface));
  margin-bottom: 6px;
  word-wrap: break-word;
}

.generic-metadata-indicators {
  margin: 6px 0;
}

/* 状态标签 */
.generic-status-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
}

.status-tag {
  font-size: 10px !important;
}

/* 置信度 */
.generic-confidence {
  margin-bottom: 8px;
}

.confidence-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.confidence-label {
  font-size: 11px;
  color: rgb(var(--v-theme-on-surface-variant));
}

.confidence-value {
  font-size: 11px;
  font-weight: 600;
  color: rgb(var(--v-theme-on-surface));
}

/* 统计信息 */
.generic-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 6px;
  border-top: 1px solid rgb(var(--v-theme-outline-variant));
}

.generic-stats-item {
  display: flex;
  align-items: center;
  font-size: 11px;
  color: rgb(var(--v-theme-outline));
}

.generic-stats-text {
  font-weight: 500;
}
</style>
