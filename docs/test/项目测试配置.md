# ⚙️ 项目测试配置

**版本**: 2.0 | **更新**: 2025-07-26 | **项目**: AI Crypto Trading Agent

## 📁 配置文件清单

### 后端测试配置

#### pytest.ini (backend/pytest.ini)
**用途**: pytest 主配置文件
**关键配置**:
```ini
[pytest]
testpaths = tests
python_files = test_*.py
asyncio_mode = auto

# 测试标记
markers =
    unit: 单元测试
    integration: 集成测试
    database: 数据库测试
    slow: 慢速测试

# 输出配置 - 统一到temp目录
addopts =
    -v --strict-markers --tb=short
    --cov=app
    --cov-report=html:../temp/backend/coverage/html
    --cov-report=xml:../temp/backend/coverage/coverage.xml
    --junitxml=../temp/backend/reports/junit.xml
    --durations=10

# 日志配置
log_cli = true
log_cli_level = INFO
```

#### conftest.py (backend/tests/conftest.py)
**用途**: pytest 全局配置和 fixture
**功能**: 数据库会话、测试用户、Mock 服务、异步支持

### 前端测试配置

#### vitest.config.ts (frontend/vitest.config.ts)
**用途**: Vitest 单元测试配置
```typescript
export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup/vitest-setup.js'],
    include: ['tests/unit/**/*.{test,spec}.{js,ts,vue}'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: '../temp/frontend/vitest/coverage'
    },
    testTimeout: 30000
  }
})
```

#### playwright.config.js (frontend/playwright.config.js)
**用途**: Playwright E2E 和 API 测试配置
```javascript
export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  retries: process.env.CI ? 2 : 0,

  // 统一输出到temp目录
  reporter: [
    ['html', { outputFolder: '../temp/frontend/playwright/html-report' }],
    ['json', { outputFile: '../temp/frontend/playwright/results.json' }],
    ['junit', { outputFile: '../temp/frontend/playwright/junit.xml' }]
  ],

  // 项目配置
  projects: [
    { name: 'api-tests', testDir: './tests/api-unified' },
    { name: 'e2e-chromium', testDir: './tests/e2e' }
  ]
})
```

### 测试环境配置

#### docker-compose.test.yml
**用途**: 测试环境 Docker 服务配置
```yaml
services:
  postgres-test:
    image: postgres:15
    environment:
      POSTGRES_DB: crypto_trader_test
      POSTGRES_USER: crypto_trader
      POSTGRES_PASSWORD: test_password_123
    ports:
      - "5432:5432"

  backend-test:
    build: { context: ./backend, dockerfile: Dockerfile }
    environment:
      DATABASE_URL: postgresql+asyncpg://crypto_trader:test_password_123@postgres-test:5432/crypto_trader_test
      TESTING: "true"
    depends_on: [postgres-test]
```

## 🔧 核心配置项

### 数据库配置

#### 连接配置
```bash
DATABASE_URL=postgresql+asyncpg://crypto_trader:test_password_123@localhost:5432/crypto_trader_test

# 配置说明
用户名: crypto_trader
密码: test_password_123
数据库: crypto_trader_test
端口: 5432
```

#### 隔离策略
- **测试**: crypto_trader_test
- **测试**: crypto_trader_test
- **生产**: crypto_trader_prod

### 服务端口配置

#### 默认端口
```bash
API_BASE_URL=http://localhost:8000      # 后端服务
FRONTEND_URL=http://localhost:5173      # 前端服务
POSTGRES_PORT=5432                      # 数据库服务
WEBSOCKET_URL=ws://localhost:8000/ws    # WebSocket 服务
```

#### 冲突处理
```bash
# 检查占用
lsof -i :8000 :5173 :5432

# 备用端口
export API_PORT=8000 FRONTEND_PORT=5173 POSTGRES_PORT=5432
```

### 超时配置

#### 测试超时设置
```javascript
// Playwright 超时
const TIMEOUTS = {
  SHORT: 5000,        // 短操作
  MEDIUM: 15000,      // 中等操作
  LONG: 30000,        // 长操作
  API_REQUEST: 20000, // API 请求
  NAVIGATION: 20000,  // 页面导航
  ELEMENT_WAIT: 8000  // 元素等待
}
```

```python
# pytest 超时
timeout = 60           # 单个测试最大时间
asyncio_timeout = 30   # 异步操作超时
```

### 并发配置

#### 并发设置
```bash
pytest -n auto                          # 自动检测CPU核心数
pytest -n 4                            # 指定4个进程
workers: process.env.CI ? 1 : undefined # Playwright并发
```

#### 安全配置
- 数据库事务隔离
- 测试数据命名空间
- 端口动态分配

### 报告配置

#### 输出目录结构
```bash
temp/
├── backend/
│   ├── coverage/ (html/, coverage.xml, coverage.json)
│   └── reports/ (junit.xml)
└── frontend/
    ├── vitest/ (coverage/, results.json, junit.xml)
    └── playwright/ (html-report/, results.json, junit.xml, artifacts/)
```

#### 报告格式
- **HTML**: 本地查看和调试
- **JSON**: CI/CD 集成和分析
- **JUnit**: 测试结果统计
- **覆盖率**: 代码质量监控

## 🌍 环境变量配置

### 必需变量
```bash
# 数据库
DATABASE_URL=postgresql+asyncpg://crypto_trader:test_password_123@localhost:5432/crypto_trader_test

# 服务
API_BASE_URL=http://localhost:8000
FRONTEND_URL=http://localhost:5173

# 测试模式
TESTING=true
SIMULATION_MODE=true
```

### 可选变量
```bash
# CI/CD
CI=true DOCKER_ENV=true

# 调试
DEBUG=true VERBOSE=true

# 性能
TEST_TIMEOUT=60 MAX_WORKERS=4
```

### 加载顺序
1. 系统环境变量 2. `.env` 文件 3. `.env.test` 文件 4. 命令行参数

## 🔒 安全配置

### 测试环境安全
```bash
# 测试专用密钥 (仅测试环境)
JWT_SECRET_KEY=test-secret-key-do-not-use-in-production
ENCRYPTION_KEY=test-encryption-key-32-characters

# 测试用户凭据
TEST_USER_PASSWORD=test_password_123
ADMIN_TEST_PASSWORD=admin_test_password_456
```

### 数据安全
- 测试数据自动清理
- 敏感信息脱敏
- 测试环境网络隔离

## 🚀 优化建议

### 性能优化
- 使用内存数据库进行快速测试
- 启用测试并发执行
- 优化测试数据创建和清理

### 稳定性优化
- 设置合理超时时间
- 实现重试机制
- 使用健康检查确保服务可用

### 可维护性优化
- 统一配置管理
- 环境配置文档化
- 配置变更版本控制

## 🔧 管理工具

### 验证脚本
```bash
python scripts/env.py test --verify-hybrid     # 检查混合环境连接
python scripts/db/check_connection.py # 验证数据库连接
python scripts/test.py --status       # 验证服务可用性
```

### 配置模板
- 开发环境配置模板
- 测试环境配置模板
- CI/CD 环境配置模板

### 配置同步
- 配置文件版本控制
- 环境配置同步脚本
- 配置变更通知机制

---
**维护**: 随配置变更及时更新 | **更新**: 2025-07-26 | **版本**: v2.0
