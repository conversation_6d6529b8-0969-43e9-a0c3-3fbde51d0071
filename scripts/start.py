#!/usr/bin/env python3
"""
混合开发环境启动脚本 - 适配版
根据《0. 项目规范.md》和《4. 开发测试环境.md》创建

核心功能：
- 启动PostgreSQL数据库服务 (Docker)
- 启动前后端开发服务器 (本地)
- 进程管理和信号处理
- 健康检查和状态监控
- 支持单独启动各个服务

混合环境特点：
- PostgreSQL在Docker中运行，提供稳定的数据库服务
- 后端和前端在本地运行，避免容器重启问题
- 支持灵活的服务组合启动

版本: 4.0 (混合环境适配版)
创建日期: 2025-08-02
"""

import argparse
import asyncio
import signal
import subprocess
import sys
import time
from pathlib import Path
from typing import List, Optional


class HybridServerManager:
    """混合开发环境服务器管理器 - 支持Docker数据库 + 本地服务"""

    def __init__(self, verbose: bool = False):
        """
        初始化服务器管理器

        Args:
            verbose: 是否显示详细输出
        """
        self.verbose = verbose
        self.project_root = Path(__file__).parent.parent
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"

        # 内联配置 - 避免复杂的配置类
        self.server_config = {
            'backend_port': 8000,
            'frontend_port': 5173,
            'backend_host': '0.0.0.0',
            'frontend_host': '0.0.0.0',
            'postgres_port': 5432,
            'compose_file': 'docker-compose.test.yml'
        }

        # 进程管理
        self.backend_process = None
        self.frontend_process = None
        self.database_started = False
        self.running = True
        
        # 验证项目结构
        if not self.backend_dir.exists():
            self.log_error(f"后端目录不存在: {self.backend_dir}")
            sys.exit(1)
        
        if not self.frontend_dir.exists():
            self.log_error(f"前端目录不存在: {self.frontend_dir}")
            sys.exit(1)
    
    def log_info(self, message: str) -> None:
        """记录信息日志"""
        print(f"[信息] {message}")
    
    def log_success(self, message: str) -> None:
        """记录成功日志"""
        print(f"\033[92m[成功] {message}\033[0m")
    
    def log_error(self, message: str) -> None:
        """记录错误日志"""
        print(f"\033[91m[错误] {message}\033[0m")
    
    def log_warning(self, message: str) -> None:
        """记录警告日志"""
        print(f"\033[93m[警告] {message}\033[0m")
    
    def print_header(self, title: str) -> None:
        """打印标题头部"""
        print(f"\n{'='*60}")
        print(f"\033[95m{title}\033[0m")
        print(f"{'='*60}")
    
    def setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.log_info("收到停止信号，正在关闭服务器...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def start_database(self) -> bool:
        """启动PostgreSQL数据库服务 (Docker)"""
        self.log_info("🗄️ 启动PostgreSQL数据库服务...")

        try:
            # 检查docker-compose文件
            compose_file = self.project_root / self.server_config['compose_file']
            if not compose_file.exists():
                self.log_error(f"找不到docker-compose文件: {compose_file}")
                return False

            # 启动PostgreSQL服务
            cmd = [
                "docker-compose", "-f", self.server_config['compose_file'],
                "up", "-d", "postgres-test"
            ]

            if self.verbose:
                self.log_info(f"执行命令: {' '.join(cmd)}")

            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                self.log_success(f"✅ PostgreSQL数据库启动成功 (端口: {self.server_config['postgres_port']})")
                self.database_started = True

                # 等待数据库完全启动
                self.log_info("等待数据库完全启动...")
                await asyncio.sleep(5)

                return True
            else:
                self.log_error(f"❌ PostgreSQL数据库启动失败: {stderr.decode()}")
                return False

        except Exception as e:
            self.log_error(f"启动数据库服务异常: {e}")
            return False

    async def stop_database(self) -> None:
        """停止PostgreSQL数据库服务"""
        if not self.database_started:
            return

        self.log_info("🛑 停止PostgreSQL数据库服务...")

        try:
            cmd = [
                "docker-compose", "-f", self.server_config['compose_file'],
                "stop", "postgres-test"
            ]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            await process.communicate()

            if process.returncode == 0:
                self.log_success("✅ PostgreSQL数据库已停止")
            else:
                self.log_warning("⚠️ 停止数据库时出现问题")

            self.database_started = False

        except Exception as e:
            self.log_error(f"停止数据库服务异常: {e}")

    async def start_backend(self) -> bool:
        """启动后端服务器"""
        self.log_info("🚀 启动后端服务器...")
        
        try:
            # 检查requirements.txt
            requirements_file = self.backend_dir / "requirements.txt"
            if not requirements_file.exists():
                self.log_error(f"找不到requirements.txt: {requirements_file}")
                return False
            
            # 启动FastAPI服务器
            cmd = [
                "python", "-m", "uvicorn",
                "app.main:app",
                "--host", self.server_config['backend_host'],
                "--port", str(self.server_config['backend_port']),
                "--reload"
            ]
            
            if self.verbose:
                self.log_info(f"执行命令: {' '.join(cmd)}")
            
            self.backend_process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=self.backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT
            )
            
            # 等待一下让服务器启动
            await asyncio.sleep(2)
            
            # 检查进程是否还在运行
            if self.backend_process.returncode is None:
                self.log_success(f"✅ 后端服务器启动成功 (端口: {self.server_config['backend_port']})")
                return True
            else:
                self.log_error("❌ 后端服务器启动失败")
                return False
                
        except Exception as e:
            self.log_error(f"启动后端服务器异常: {e}")
            return False
    
    async def start_frontend(self) -> bool:
        """启动前端服务器"""
        self.log_info("🌐 启动前端服务器...")
        
        try:
            # 检查package.json
            package_json = self.frontend_dir / "package.json"
            if not package_json.exists():
                self.log_error(f"找不到package.json: {package_json}")
                return False
            
            # 启动Vite开发服务器
            cmd = ["npm", "run", "dev"]
            
            if self.verbose:
                self.log_info(f"执行命令: {' '.join(cmd)}")
            
            self.frontend_process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=self.frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT
            )
            
            # 等待一下让服务器启动
            await asyncio.sleep(3)
            
            # 检查进程是否还在运行
            if self.frontend_process.returncode is None:
                self.log_success(f"✅ 前端服务器启动成功 (端口: {self.server_config['frontend_port']})")
                return True
            else:
                self.log_error("❌ 前端服务器启动失败")
                return False
                
        except Exception as e:
            self.log_error(f"启动前端服务器异常: {e}")
            return False
    
    async def stop_servers(self) -> None:
        """停止所有服务器"""
        self.log_info("🛑 停止服务器...")

        # 停止后端服务器
        if self.backend_process and self.backend_process.returncode is None:
            self.log_info("停止后端服务器...")
            self.backend_process.terminate()
            try:
                await asyncio.wait_for(self.backend_process.wait(), timeout=5)
                self.log_success("✅ 后端服务器已停止")
            except asyncio.TimeoutError:
                self.log_warning("⚠️ 后端服务器强制终止")
                self.backend_process.kill()
                await self.backend_process.wait()

        # 停止前端服务器
        if self.frontend_process and self.frontend_process.returncode is None:
            self.log_info("停止前端服务器...")
            self.frontend_process.terminate()
            try:
                await asyncio.wait_for(self.frontend_process.wait(), timeout=5)
                self.log_success("✅ 前端服务器已停止")
            except asyncio.TimeoutError:
                self.log_warning("⚠️ 前端服务器强制终止")
                self.frontend_process.kill()
                await self.frontend_process.wait()

        # 停止数据库服务器
        await self.stop_database()
    
    async def monitor_servers(self) -> None:
        """监控服务器状态"""
        while self.running:
            # 检查后端进程
            if self.backend_process and self.backend_process.returncode is not None:
                self.log_error("❌ 后端服务器意外退出")
                self.running = False
                break
            
            # 检查前端进程
            if self.frontend_process and self.frontend_process.returncode is not None:
                self.log_error("❌ 前端服务器意外退出")
                self.running = False
                break
            
            await asyncio.sleep(1)
    
    async def start_all_services(self) -> bool:
        """启动完整的混合开发环境 (数据库 + 后端 + 前端)"""
        self.print_header("🚀 启动混合开发环境 - 完整版")

        # 设置信号处理
        self.setup_signal_handlers()

        # 启动数据库
        if not await self.start_database():
            return False

        # 启动后端
        if not await self.start_backend():
            await self.stop_servers()
            return False

        # 启动前端
        if not await self.start_frontend():
            await self.stop_servers()
            return False

        # 显示访问信息
        self.print_access_info()

        # 监控服务器
        try:
            await self.monitor_servers()
        except KeyboardInterrupt:
            self.log_info("收到中断信号")
        finally:
            await self.stop_servers()

        return True

    async def start_database_only(self) -> bool:
        """只启动数据库服务"""
        self.print_header("🗄️ 启动PostgreSQL数据库服务")

        # 设置信号处理
        self.setup_signal_handlers()

        # 启动数据库
        if not await self.start_database():
            return False

        # 显示访问信息
        self.log_info(f"PostgreSQL数据库地址: localhost:{self.server_config['postgres_port']}")
        self.log_info("数据库: crypto_trader_test")
        self.log_info("用户名: crypto_trader")
        self.log_info("按 Ctrl+C 停止数据库服务")

        # 等待中断信号
        try:
            while self.running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            self.log_info("收到中断信号")
        finally:
            await self.stop_database()

        return True
    
    async def start_backend_only(self) -> bool:
        """只启动后端服务器 (需要数据库已运行)"""
        self.print_header("🚀 启动后端服务器")

        # 设置信号处理
        self.setup_signal_handlers()

        # 启动后端
        if not await self.start_backend():
            return False

        # 显示访问信息
        self.log_info(f"后端服务器地址: http://localhost:{self.server_config['backend_port']}")
        self.log_info(f"API文档: http://localhost:{self.server_config['backend_port']}/docs")
        self.log_info("注意: 确保PostgreSQL数据库已启动")
        self.log_info("按 Ctrl+C 停止服务器")

        # 监控服务器
        try:
            while self.running and self.backend_process.returncode is None:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            self.log_info("收到中断信号")
        finally:
            await self.stop_servers()

        return True
    
    async def start_frontend_only(self) -> bool:
        """只启动前端服务器 (需要后端已运行)"""
        self.print_header("🌐 启动前端服务器")

        # 设置信号处理
        self.setup_signal_handlers()

        # 启动前端
        if not await self.start_frontend():
            return False

        # 显示访问信息
        self.log_info(f"前端服务器地址: http://localhost:{self.server_config['frontend_port']}")
        self.log_info("注意: 确保后端服务器已启动")
        self.log_info("按 Ctrl+C 停止服务器")

        # 监控服务器
        try:
            while self.running and self.frontend_process.returncode is None:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            self.log_info("收到中断信号")
        finally:
            await self.stop_servers()

        return True
    
    def print_access_info(self) -> None:
        """显示访问信息"""
        self.print_header("📖 混合开发环境访问信息")
        self.log_info("混合开发环境已启动，可以通过以下地址访问：")
        print(f"  🌐 前端: \033[96mhttp://localhost:{self.server_config['frontend_port']}\033[0m")
        print(f"  🚀 后端: \033[96mhttp://localhost:{self.server_config['backend_port']}\033[0m")
        print(f"  📚 API文档: \033[96mhttp://localhost:{self.server_config['backend_port']}/docs\033[0m")
        print(f"  🗄️ 数据库: \033[96mlocalhost:{self.server_config['postgres_port']}\033[0m (crypto_trader_test)")
        self.log_info("按 Ctrl+C 停止所有服务")


def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="混合开发环境启动脚本 - 适配版",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🚀 混合环境启动选项:
  all        - 启动完整环境 (数据库 + 后端 + 前端) (默认)
  database   - 只启动PostgreSQL数据库服务
  backend    - 只启动后端服务器 (需要数据库已运行)
  frontend   - 只启动前端服务器 (需要后端已运行)

🚀 使用示例:
  python scripts/start.py                # 启动完整环境
  python scripts/start.py database       # 只启动数据库
  python scripts/start.py backend        # 只启动后端
  python scripts/start.py frontend       # 只启动前端
  python scripts/start.py --verbose      # 显示详细日志

📋 服务信息:
  数据库: localhost:5432 (crypto_trader_test)
  后端服务器: http://localhost:8000
  前端服务器: http://localhost:5173
  API文档: http://localhost:8000/docs

🔧 混合环境特点:
  - PostgreSQL在Docker中运行，提供稳定的数据库服务
  - 后端和前端在本地运行，避免容器重启问题
  - 支持灵活的服务组合启动
        """
    )

    # 位置参数
    parser.add_argument("target", nargs='?', default="all",
                       choices=["all", "database", "backend", "frontend"],
                       help="要启动的服务类型 (默认: all)")

    # 可选参数
    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细日志")

    return parser


async def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()

    # 创建服务器管理器
    manager = HybridServerManager(verbose=args.verbose)

    success = True

    try:
        # 根据参数启动相应服务
        if args.target == "database":
            success = await manager.start_database_only()
        elif args.target == "backend":
            success = await manager.start_backend_only()
        elif args.target == "frontend":
            success = await manager.start_frontend_only()
        else:  # all
            success = await manager.start_all_services()

    except KeyboardInterrupt:
        manager.log_warning("操作被用户中断")
        success = False
    except Exception as e:
        manager.log_error(f"执行过程中发生异常: {e}")
        if args.verbose:
            import traceback
            manager.log_error(f"详细错误信息:\n{traceback.format_exc()}")
        success = False

    # 输出最终结果
    if success:
        manager.log_success("\n✅ 操作完成")
    else:
        manager.log_error("\n❌ 操作失败")

    sys.exit(0 if success else 1)


if __name__ == '__main__':
    # 检查Python版本
    if sys.version_info < (3, 11):
        print("❌ 错误: 需要Python 3.11或更高版本")
        sys.exit(1)

    # 运行主函数
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
        sys.exit(1)
