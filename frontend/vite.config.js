import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { VitePWA } from 'vite-plugin-pwa'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载根目录的环境变量
  const env = loadEnv(mode, path.resolve(__dirname, '..'), '')

  return {
  plugins: [
    vue(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/api\./,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'api-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 // 24 hours
              }
            }
          }
        ]
      },
      manifest: {
        name: 'Crypto Trader - AI Agent 驱动的加密货币智能跟单系统',
        short_name: 'Crypto Trader',
        description: 'AI-powered cryptocurrency trading platform',
        theme_color: '#1976d2',
        background_color: '#ffffff',
        display: 'standalone',
        orientation: 'portrait',
        scope: '/',
        start_url: '/',
        icons: [
        {
          src: '/icon.svg',
          sizes: '192x192',
          type: 'image/svg+xml',
          purpose: 'any maskable'
        },
        {
          src: '/icon.svg',
          sizes: '512x512',
          type: 'image/svg+xml',
          purpose: 'any maskable'
        }
      ]
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    host: '0.0.0.0', // 允许外部访问（Docker环境需要）
    port: 5173,
    strictPort: true, // 端口被占用时不自动尝试下一个端口
    // 优化的HMR配置，解决连接丢失问题
    hmr: {
      port: env.VITE_HMR_PORT ? parseInt(env.VITE_HMR_PORT) : 24678,
      host: env.VITE_HMR_HOST || 'localhost', // 本地开发使用localhost更稳定
      protocol: 'ws',
      // 增加连接超时时间
      timeout: 60000,
      // 启用心跳检测
      overlay: true,
      // 客户端路径配置，确保WebSocket连接正确
      clientPort: env.VITE_HMR_PORT ? parseInt(env.VITE_HMR_PORT) : 24678
    },
    watch: {
      usePolling: process.env.VITE_USE_POLLING === 'true' || process.env.CHOKIDAR_USEPOLLING === 'true',
      interval: process.env.VITE_POLLING_INTERVAL ? parseInt(process.env.VITE_POLLING_INTERVAL) : 1000,
      // 忽略更多文件以提高性能和稳定性
      ignored: [
        '**/node_modules/**',
        '**/dist/**',
        '**/coverage/**',
        '**/test-results/**',
        '**/.git/**',
        '**/temp/**'
      ],
      // 增加文件监听器的稳定性
      awaitWriteFinish: {
        stabilityThreshold: 100,
        pollInterval: 100
      }
    },
    // 增加连接超时时间
    middlewareMode: false,
    // 优化代理配置
    proxy: {
      '/api': {
        target: env.VITE_API_BASE_URL || 'http://localhost:8000',
        changeOrigin: true,
        timeout: 30000, // 30秒超时
        proxyTimeout: 30000,
        // 增加错误处理
        onError: (err) => {
          console.error('Proxy error:', err);
        }
      },
      '/ws': {
        target: env.VITE_WS_URL || 'ws://localhost:8000',
        ws: true,
        timeout: 30000
      }
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // 将Vue相关库分离到单独的chunk
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          // 将Vuetify分离到单独的chunk
          'vuetify-vendor': ['vuetify']
        }
      }
    },
    // 启用gzip压缩
    reportCompressedSize: true,
    // 设置chunk大小警告限制
    chunkSizeWarningLimit: 1000
  },
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'vuetify'
    ]
  }
  }
})
