# Discord配置数据库迁移 - 简化版设计文档

## 1. 项目背景

### 1.1 当前状态
- Discord配置通过环境变量（.env.test）管理
- 配置格式：`DISCORD_FILTER_CONFIG={"enabled":false,"server_ids":[],"channel_ids":[],"author_ids":[],"allowed_message_types":["text"]}`
- 单一Discord监听器实例，服务所有用户

### 1.2 目标
- 将Discord配置迁移到数据库存储
- 支持用户级别的配置管理
- 简化开发阶段的实现复杂度
- 为后续多用户架构奠定基础

## 2. 简化设计原则

### 2.1 开发阶段简化
- **数据迁移**：直接重建表，无需兼容性考虑
- **用户管理**：假设系统中只有一个有效配置
- **监听器架构**：保持单一实例，从数据库读取配置
- **功能范围**：只实现核心CRUD功能

### 2.2 核心配置字段（6个）
```python
# 基础配置
enabled: bool                    # 是否启用Discord监听
encrypted_token: Optional[str]   # 加密的Discord Token

# 过滤配置  
server_ids: List[str]           # 监控的服务器ID列表
channel_ids: List[str]          # 监控的频道ID列表
author_ids: List[str]           # 监控的作者ID列表
allowed_message_types: List[str] # 允许的消息类型
```

### 2.3 系统级配置（继续使用环境变量）
```bash
# 高级配置保持在环境变量中
DISCORD_AUTO_START=true
DISCORD_RECONNECT_ATTEMPTS=5
DISCORD_RECONNECT_DELAY=30
DISCORD_MESSAGE_CACHE_SIZE=1000
DISCORD_DEDUPLICATION_WINDOW=5
DISCORD_PROXY=
```

## 3. 数据库设计

### 3.1 表结构
```sql
CREATE TABLE discord_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- 基础配置
    enabled BOOLEAN NOT NULL DEFAULT false,
    encrypted_token TEXT,
    
    -- 过滤配置
    server_ids TEXT[] DEFAULT '{}',
    channel_ids TEXT[] DEFAULT '{}',
    author_ids TEXT[] DEFAULT '{}',
    allowed_message_types TEXT[] DEFAULT '{"text"}',
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- 约束
    CONSTRAINT valid_message_types CHECK (
        allowed_message_types <@ ARRAY['text', 'embed', 'attachment', 'reply']
    ),
    UNIQUE(user_id)
);

CREATE INDEX idx_discord_configs_user_id ON discord_configs(user_id);
CREATE INDEX idx_discord_configs_enabled ON discord_configs(enabled);
```

### 3.2 数据迁移策略
```sql
-- 开发阶段：直接删除重建
DROP TABLE IF EXISTS discord_configs CASCADE;
-- 然后执行上述CREATE TABLE语句
```

## 4. 后端实现

### 4.1 数据模型（已完成）
```python
# backend/app/core/models.py
class DiscordConfig(Base):
    __tablename__ = "discord_configs"
    # ... 6个核心字段
```

### 4.2 API Schema（已完成）
```python
# backend/app/core/schemas.py
class DiscordConfigRequest(BaseModel):
    enabled: bool = False
    token: Optional[str] = None
    server_ids: List[str] = []
    channel_ids: List[str] = []
    author_ids: List[str] = []
    allowed_message_types: List[str] = ["text"]

class DiscordConfigResponse(BaseModel):
    # ... 响应字段
```

### 4.3 API接口设计
```python
# backend/app/api/v1/configs.py

# 基础CRUD接口
GET    /api/v1/configs/discord     # 获取当前用户配置
POST   /api/v1/configs/discord     # 创建配置
PUT    /api/v1/configs/discord     # 更新配置
DELETE /api/v1/configs/discord     # 删除配置
```

### 4.4 Discord监听器修改
```python
# backend/app/services/discord_listener.py

class TradingSignalClient:
    async def load_config_from_db(self):
        """从数据库加载配置，替代环境变量"""
        # 查询启用的配置（假设只有一个）
        # 如果没有配置，使用默认值
        
    async def reload_config(self):
        """重新加载配置并重启连接"""
        # 用于配置更新时的热重载
```

### 4.5 简化的配置管理器
```python
# backend/app/services/simple_discord_manager.py

class SimpleDiscordManager:
    """简化的Discord配置管理器 - 单用户版本"""
    
    def __init__(self):
        self.current_client: Optional[TradingSignalClient] = None
        
    async def reload_from_db(self):
        """从数据库重新加载配置"""
        
    async def restart_listener(self):
        """重启Discord监听器"""
        
    async def get_status(self):
        """获取当前状态"""
```

## 5. 前端实现

### 5.1 配置界面集成
```vue
<!-- frontend/src/views/ConfigsView.vue -->

<!-- 添加Discord配置选项卡 -->
<v-tab value="discord">
  <v-icon start>mdi-discord</v-icon>
  Discord配置
</v-tab>

<!-- Discord配置表单 -->
<v-window-item value="discord">
  <DiscordConfigForm />
</v-window-item>
```

### 5.2 配置表单组件
```vue
<!-- frontend/src/components/DiscordConfigForm.vue -->

<template>
  <v-form>
    <!-- 启用开关 -->
    <v-switch v-model="config.enabled" label="启用Discord监听" />
    
    <!-- Token输入 -->
    <v-text-field 
      v-model="config.token" 
      label="Discord Token" 
      type="password" 
    />
    
    <!-- ID列表输入 -->
    <v-text-field v-model="serverIdsText" label="服务器ID列表" />
    <v-text-field v-model="channelIdsText" label="频道ID列表" />
    <v-text-field v-model="authorIdsText" label="作者ID列表" />
    
    <!-- 消息类型选择 -->
    <v-select 
      v-model="config.allowed_message_types"
      :items="messageTypeOptions"
      label="允许的消息类型"
      multiple
    />
    
    <!-- 操作按钮 -->
    <v-btn @click="saveConfig">保存配置</v-btn>
  </v-form>
</template>
```

## 6. 实施步骤

### 6.1 Phase 1: 数据库和模型
- [x] 创建DiscordConfig模型
- [x] 创建API Schema
- [ ] 创建数据库迁移脚本
- [ ] 更新数据库初始化脚本

### 6.2 Phase 2: 后端API
- [ ] 实现Discord配置CRUD接口
- [ ] 修改discord_listener.py从数据库读取配置
- [ ] 创建简化的配置管理器
- [ ] 实现配置更新时的监听器重启

### 6.3 Phase 3: 前端界面
- [ ] 在ConfigsView.vue中添加Discord选项卡
- [ ] 创建DiscordConfigForm组件
- [ ] 集成API调用和状态管理
- [ ] 添加表单验证和错误处理

### 6.4 Phase 4: 测试和优化
- [ ] 测试配置CRUD功能
- [ ] 测试监听器重启机制
- [ ] 验证前端界面交互
- [ ] 性能和稳定性测试

## 7. 技术考虑

### 7.1 配置加载优先级
1. 数据库中的用户配置（优先）
2. 环境变量默认配置（fallback）
3. 硬编码默认值（最后）

### 7.2 监听器重启策略
- 配置更新时自动重启
- 保持现有连接状态管理
- 错误处理和重连机制

### 7.3 安全考虑
- Discord Token加密存储
- API权限验证
- 敏感信息不在响应中返回

### 7.4 性能优化
- 配置缓存机制
- 避免频繁数据库查询
- 异步处理监听器重启

## 8. 后续扩展计划

### 8.1 多用户支持
- 每个用户独立的Discord监听器
- 用户级别的配置隔离
- 资源管理和限制

### 8.2 高级功能
- 配置模板和预设
- 配置导入导出
- 实时状态监控
- 配置历史记录

### 8.3 运维功能
- 配置备份和恢复
- 批量配置管理
- 监控和告警
- 性能分析

## 9. 风险和限制

### 9.1 当前限制
- 只支持单一有效配置
- 无配置冲突检测
- 简化的错误处理

### 9.2 潜在风险
- 配置更新可能导致服务中断
- 数据库故障影响Discord功能
- Token安全性依赖加密实现

### 9.3 缓解措施
- 实现配置验证
- 添加回滚机制
- 完善错误处理和日志记录
