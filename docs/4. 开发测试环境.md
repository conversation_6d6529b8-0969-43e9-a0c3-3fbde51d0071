# 4. 开发测试环境

## 目录
1. [混合开发环境概述](#1-混合开发环境概述)
2. [环境架构说明](#2-环境架构说明)
3. [PostgreSQL 数据库设置](#3-postgresql-数据库设置)
4. [后端本地设置](#4-后端本地设置)
5. [前端本地设置](#5-前端本地设置)
6. [环境变量配置](#6-环境变量配置)
7. [端口映射说明](#7-端口映射说明)
8. [开发流程指南](#8-开发流程指南)
9. [故障排除](#9-故障排除)

---

# 1. 混合开发环境概述

## 1.1 设计理念

由于 Docker 容器重启问题导致开发进度受阻，我们采用混合开发环境的方法：
- **PostgreSQL**: 在 Docker 中运行，提供稳定的数据库服务
- **后端服务**: 在本地运行，避免容器重启问题，支持快速开发迭代
- **前端服务**: 在本地运行，享受原生的热重载和调试体验

## 1.2 优势

- ✅ **稳定性**: 避免 Docker 容器重启导致的开发中断
- ✅ **性能**: 本地运行的服务响应更快
- ✅ **调试**: 更好的调试体验和错误追踪
- ✅ **热重载**: 原生的文件监听和自动重启
- ✅ **兼容性**: 保持与现有开发流程的兼容

---

# 2. 环境架构说明

## 2.1 服务分布

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端服务      │    │   后端服务      │    │  PostgreSQL     │
│  (本地运行)     │    │  (本地运行)     │    │ (Docker运行)    │
│                 │    │                 │    │                 │
│ localhost:5173  │───▶│ localhost:8000  │───▶│ localhost:5432  │
│                 │    │                 │    │                 │
│ Vue.js + Vite   │    │ FastAPI + Python│    │ postgres:15     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 2.2 数据流

1. **前端** → **后端**: HTTP API 调用 (localhost:8000)
2. **后端** → **数据库**: PostgreSQL 连接 (localhost:5432)
3. **开发者** → **前端**: 浏览器访问 (localhost:5173)

---

# 3. PostgreSQL 数据库设置

## 3.1 启动数据库服务

```bash
# 启动 PostgreSQL 容器
docker-compose -f docker-compose.test.yml up -d postgres-test

# 检查服务状态
docker-compose -f docker-compose.test.yml ps
```

## 3.2 数据库连接信息

- **主机**: localhost
- **端口**: 5432
- **数据库**: crypto_trader_test
- **用户名**: crypto_trader
- **密码**: test_password_123

## 3.3 数据库初始化

```bash
# 初始化数据库表结构
python scripts/db/run_sqls.py --init

# 创建测试用户
python scripts/db/run_sqls.py --create-user

# 生成测试数据
python scripts/db/run_sqls.py --mock-data

# 一键完成所有初始化
python scripts/db/run_sqls.py --all
```

## 3.4 数据库管理

```bash
# 连接到数据库
docker exec -it crypto_trader_postgres_test psql -U crypto_trader -d crypto_trader_test

# 查看数据库日志
docker logs crypto_trader_postgres_test

# 停止数据库
docker-compose -f docker-compose.test.yml stop postgres-test

# 重启数据库
docker-compose -f docker-compose.test.yml restart postgres-test
```

---

# 4. 后端本地设置

## 4.1 环境准备

### Python 环境
```bash
# 确保使用正确的 Python 环境
conda activate crypto-trader
# 或者
source venv/bin/activate
```

### 依赖安装
```bash
cd backend
pip install -r requirements.txt
```

## 4.2 环境变量配置

创建 `.env.local` 文件：
```bash
# 数据库连接 (连接到 Docker 中的 PostgreSQL)
DATABASE_URL=postgresql+asyncpg://crypto_trader:test_password_123@localhost:5432/crypto_trader_test

# 应用配置
TESTING=true
DEBUG=true
LOG_LEVEL=DEBUG
SIMULATION_MODE=true

# API 配置
API_HOST=0.0.0.0
API_PORT=8000
API_URL=http://localhost:8000

# CORS 配置
CORS_ORIGINS=http://localhost:5173,http://localhost:3000
FRONTEND_URL=http://localhost:5173

# LLM 配置 (可选，用于测试)
OPENAI_API_KEY=your-openai-api-key-here
```

## 4.3 启动后端服务

```bash
cd backend

# 方式1: 使用 uvicorn 直接启动 (推荐)
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 方式2: 使用 Python 模块启动
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 方式3: 使用项目脚本启动
python scripts/start.py --backend-only
```

## 4.4 验证后端服务

```bash
# 检查健康状态
curl http://localhost:8000/api/v1/system/health

# 查看 API 文档
open http://localhost:8000/docs
```

---

# 5. 前端本地设置

## 5.1 环境准备

### Node.js 环境
```bash
# 确保 Node.js 版本 >= 18
node --version
npm --version
```

### 依赖安装
```bash
cd frontend
npm install
```

## 5.2 环境变量配置

创建 `.env.local` 文件：
```bash
# API 配置 (连接到本地后端)
VITE_API_BASE_URL=http://localhost:8000
API_BASE_URL=http://localhost:8000

# 开发服务器配置
VITE_HOST=0.0.0.0
VITE_PORT=5173

# 开发模式配置
NODE_ENV=development
VITE_DEV_MODE=true
```

## 5.3 启动前端服务

```bash
cd frontend

# 启动开发服务器
npm run dev

# 或者指定端口启动
npm run dev -- --port 5173 --host 0.0.0.0
```

## 5.4 验证前端服务

```bash
# 浏览器访问
open http://localhost:5173

# 检查热重载
# 修改任意 .vue 文件，观察浏览器自动刷新
```

---

# 6. 环境变量配置

## 6.1 后端环境变量

| 变量名 | 值 | 说明 |
|--------|----|----|
| `DATABASE_URL` | `postgresql+asyncpg://crypto_trader:test_password_123@localhost:5432/crypto_trader_test` | 数据库连接字符串 |
| `API_HOST` | `0.0.0.0` | API 服务监听地址 |
| `API_PORT` | `8000` | API 服务端口 |
| `CORS_ORIGINS` | `http://localhost:5173,http://localhost:3000` | 允许的跨域来源 |
| `DEBUG` | `true` | 开启调试模式 |
| `LOG_LEVEL` | `DEBUG` | 日志级别 |

## 6.2 前端环境变量

| 变量名 | 值 | 说明 |
|--------|----|----|
| `VITE_API_BASE_URL` | `http://localhost:8000` | 后端 API 基础 URL |
| `VITE_HOST` | `0.0.0.0` | 开发服务器监听地址 |
| `VITE_PORT` | `5173` | 开发服务器端口 |
| `NODE_ENV` | `development` | Node.js 环境模式 |

---

# 7. 端口映射说明

## 7.1 服务端口分配

| 服务 | 端口 | 访问地址 | 说明 |
|------|------|----------|------|
| PostgreSQL | 5432 | localhost:5432 | 数据库服务 |
| 后端 API | 8000 | http://localhost:8000 | FastAPI 服务 |
| 前端开发服务器 | 5173 | http://localhost:5173 | Vite 开发服务器 |
| API 文档 | 8000 | http://localhost:8000/docs | Swagger UI |

## 7.2 端口冲突解决

```bash
# 检查端口占用
lsof -i :5432 -i :8000 -i :5173

# 杀死占用端口的进程
kill -9 <PID>

# 使用不同端口启动服务
uvicorn app.main:app --port 8001  # 后端使用 8001
npm run dev -- --port 5174       # 前端使用 5174
```

---

# 8. 开发流程指南

## 8.1 日常开发启动流程

```bash
# 1. 启动数据库
docker-compose -f docker-compose.test.yml up -d postgres-test

# 2. 启动后端 (新终端)
cd backend
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 3. 启动前端 (新终端)
cd frontend
npm run dev

# 4. 验证服务
curl http://localhost:8000/api/v1/system/health
open http://localhost:5173
```

## 8.2 测试执行流程

```bash
# 后端测试
cd backend
pytest tests/unit/
pytest tests/integration/

# 前端测试
cd frontend
npm run test:unit
npm run test:components

# API 集成测试
cd frontend
npm run test:api
```

## 8.3 代码修改流程

1. **修改后端代码**: uvicorn 自动重启，无需手动操作
2. **修改前端代码**: Vite 热重载，浏览器自动更新
3. **修改数据库结构**: 运行 `python scripts/db/run_sqls.py --init`
4. **添加新依赖**: 
   - 后端: `pip install <package>` + 更新 `requirements.txt`
   - 前端: `npm install <package>`

---

# 9. 故障排除

## 9.1 数据库连接问题

### 问题: 后端无法连接数据库
```bash
# 检查 PostgreSQL 容器状态
docker ps | grep postgres

# 检查数据库日志
docker logs crypto_trader_postgres_test

# 重启数据库容器
docker-compose -f docker-compose.test.yml restart postgres-test

# 测试数据库连接
docker exec -it crypto_trader_postgres_test psql -U crypto_trader -d crypto_trader_test -c "SELECT 1;"
```

### 问题: 数据库初始化失败
```bash
# 清理并重新初始化
docker-compose -f docker-compose.test.yml down -v
docker-compose -f docker-compose.test.yml up -d postgres-test
python scripts/db/run_sqls.py --all
```

## 9.2 后端服务问题

### 问题: 后端启动失败
```bash
# 检查 Python 环境
which python
python --version

# 检查依赖
pip list | grep fastapi

# 检查环境变量
echo $DATABASE_URL

# 重新安装依赖
pip install -r requirements.txt
```

### 问题: API 请求失败
```bash
# 检查后端日志
tail -f backend/logs/app.log

# 测试 API 端点
curl -v http://localhost:8000/api/v1/system/health

# 检查 CORS 配置
curl -H "Origin: http://localhost:5173" http://localhost:8000/api/v1/system/health
```

## 9.3 前端服务问题

### 问题: 前端启动失败
```bash
# 检查 Node.js 版本
node --version

# 清理并重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 检查端口占用
lsof -i :5173
```

### 问题: 热重载不工作
```bash
# 检查文件监听
npm run dev -- --force

# 清理 Vite 缓存
rm -rf .vite
npm run dev
```

### 问题: API 调用失败
```bash
# 检查网络请求 (浏览器开发者工具)
# 1. 打开 Network 标签
# 2. 刷新页面
# 3. 查看 API 请求状态

# 检查环境变量
echo $VITE_API_BASE_URL

# 测试后端连接
curl http://localhost:8000/api/v1/system/health
```

## 9.4 常见错误解决

### 错误: "Connection refused"
- 检查服务是否启动
- 检查端口是否正确
- 检查防火墙设置

### 错误: "CORS policy"
- 检查后端 CORS 配置
- 确认前端请求地址正确
- 重启后端服务

### 错误: "Module not found"
- 重新安装依赖
- 检查 Python/Node.js 环境
- 清理缓存文件

---

## 总结

混合开发环境提供了稳定可靠的开发体验，避免了 Docker 容器重启问题。通过本文档的指导，开发团队可以快速搭建和使用这个环境，提高开发效率。

**关键要点**:
- PostgreSQL 在 Docker 中稳定运行
- 后端和前端在本地运行，享受原生性能
- 保持了完整的开发功能和测试能力
- 提供了详细的故障排除指南

**下一步**:
- 根据实际使用情况优化配置
- 添加更多自动化脚本
- 完善监控和日志收集

---

# 10. 快速参考

## 10.1 常用命令

### 数据库操作
```bash
# 启动数据库
docker-compose -f docker-compose.test.yml up -d postgres-test

# 初始化数据库
python scripts/db/run_sqls.py --all

# 连接数据库
docker exec -it crypto_trader_postgres_test psql -U crypto_trader -d crypto_trader_test

# 停止数据库
docker-compose -f docker-compose.test.yml stop postgres-test
```

### 后端操作
```bash
# 启动后端
cd backend && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 运行后端测试
cd backend && pytest tests/

# 检查后端健康
curl http://localhost:8000/api/v1/system/health
```

### 前端操作
```bash
# 启动前端
cd frontend && npm run dev

# 运行前端测试
cd frontend && npm run test:unit

# 构建前端
cd frontend && npm run build
```

## 10.2 环境检查清单

- [ ] PostgreSQL 容器运行正常
- [ ] 数据库连接测试通过
- [ ] 后端服务启动成功
- [ ] 后端健康检查通过
- [ ] 前端服务启动成功
- [ ] 前端可以访问后端 API
- [ ] 热重载功能正常工作

## 10.3 性能优化建议

### 数据库优化
- 使用 SSD 存储 Docker 卷
- 调整 PostgreSQL 配置参数
- 定期清理测试数据

### 后端优化
- 使用虚拟环境隔离依赖
- 启用代码缓存和预编译
- 优化日志输出级别

### 前端优化
- 使用 npm 缓存加速安装
- 启用 Vite 的构建缓存
- 优化热重载监听范围

---

# 11. 迁移指南

## 11.1 从 Docker Compose 全栈迁移

如果你之前使用完整的 Docker Compose 环境，按以下步骤迁移：

### 步骤 1: 停止现有服务
```bash
docker-compose -f docker-compose.test.yml down
```

### 步骤 2: 备份数据 (可选)
```bash
docker-compose -f docker-compose.test.yml up -d postgres-test
docker exec crypto_trader_postgres_test pg_dump -U crypto_trader crypto_trader_test > backup.sql
docker-compose -f docker-compose.test.yml stop postgres-test
```

### 步骤 3: 更新配置文件
- 使用新的 `docker-compose.test.yml` (已更新)
- 创建本地环境变量文件

### 步骤 4: 启动混合环境
```bash
# 启动数据库
docker-compose -f docker-compose.test.yml up -d postgres-test

# 恢复数据 (如果有备份)
docker exec -i crypto_trader_postgres_test psql -U crypto_trader crypto_trader_test < backup.sql

# 启动本地服务
cd backend && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
cd frontend && npm run dev &
```

## 11.2 回退到 Docker Compose

如果需要回退到完整的 Docker Compose 环境：

### 步骤 1: 恢复原始配置
```bash
git checkout HEAD~1 -- docker-compose.test.yml
```

### 步骤 2: 重新构建镜像
```bash
docker-compose -f docker-compose.test.yml build
```

### 步骤 3: 启动完整环境
```bash
docker-compose -f docker-compose.test.yml up -d
```

---

# 12. 最佳实践

## 12.1 开发习惯

### 代码修改
- 保持小步快跑的开发节奏
- 及时提交代码变更
- 定期运行测试验证功能

### 环境管理
- 每日启动时检查服务状态
- 定期清理日志文件
- 保持依赖版本同步

### 调试技巧
- 使用浏览器开发者工具调试前端
- 使用 IDE 断点调试后端
- 查看服务日志定位问题

## 12.2 团队协作

### 环境同步
- 统一使用相同的环境配置
- 共享环境变量模板
- 文档化特殊配置需求

### 问题反馈
- 及时报告环境问题
- 分享解决方案和经验
- 更新文档和最佳实践

---

# 13. 附录

## 13.1 相关文档链接

- [项目规范](./0.%20项目规范.md)
- [后端设计文档](./2.%20后端设计文档.md)
- [前端设计文档](./3.%20前端设计文档.md)
- [测试部署运维文档](./4.%20测试部署运维文档.md)

## 13.2 外部资源

- [FastAPI 官方文档](https://fastapi.tiangolo.com/)
- [Vue.js 官方文档](https://vuejs.org/)
- [Vite 官方文档](https://vitejs.dev/)
- [PostgreSQL 官方文档](https://www.postgresql.org/docs/)

## 13.3 版本信息

- **文档版本**: 1.0.0
- **创建日期**: 2025-08-02
- **最后更新**: 2025-08-02
- **适用项目版本**: crypto_trader v1.0.0

---

**文档维护**: 请在环境配置发生变化时及时更新本文档，确保团队成员能够获得最新的设置指南。
