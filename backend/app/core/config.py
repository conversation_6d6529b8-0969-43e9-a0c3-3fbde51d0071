"""应用配置模块 - 使用 Pydantic 读取环境变量，管理应用配置"""
import os
from enum import Enum
from functools import lru_cache
from typing import Any, Dict, List, Optional, Union, Set

import structlog
from dotenv import load_dotenv
from pydantic import AnyHttpUrl, BaseModel, Field, field_validator, validator
from pydantic_settings import BaseSettings, SettingsConfigDict

# 配置结构化日志
logger = structlog.get_logger()

# 加载环境变量
load_dotenv()


class LogLevel(str, Enum):
    """日志级别枚举"""

    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LLMProvider(str, Enum):
    """支持的LLM提供商"""

    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    CUSTOM = "custom"


class DatabaseSettings(BaseModel):
    """数据库相关配置 - 现在作为数据传输对象使用"""

    url: str = "postgresql+asyncpg://crypto_trader:test_password_123@localhost:5432/crypto_trader_test"
    pool_size: int = 5
    max_overflow: int = 10
    pool_timeout: int = 30
    pool_recycle: int = 1800
    echo: bool = False


class SecuritySettings(BaseModel):
    """安全相关配置 - 现在作为数据传输对象使用"""

    secret_key: str = "development_secret_key_min_32_chars_here_change_in_production"
    jwt_secret_key: str = (
        "development_jwt_secret_key_min_32_chars_here_change_in_production"
    )
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 30
    password_hash_rounds: int = 12
    salt: bytes = b"crypto_trader_salt"


class JWTSettings(BaseModel):
    """JWT配置 - 兼容性接口"""

    secret_key: str = (
        "development_jwt_secret_key_min_32_chars_here_change_in_production"
    )
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7


class APISettings(BaseModel):
    """API相关配置 - 现在作为数据传输对象使用"""

    host: str = "0.0.0.0"
    port: int = 8000
    prefix: str = "/api/v1"
    url: str = "http://localhost:8000"
    cors_origins_raw: Optional[str] = None

    @property
    def cors_origins(self) -> List[str]:
        """动态解析CORS origins配置"""
        if not self.cors_origins_raw:
            return ["http://localhost:3000", "http://localhost:8080"]

        raw_value = self.cors_origins_raw.strip()
        if not raw_value:
            return ["http://localhost:3000", "http://localhost:8080"]

        # 尝试JSON解析（支持数组格式）
        if raw_value.startswith("[") and raw_value.endswith("]"):
            try:
                import json

                return json.loads(raw_value)
            except json.JSONDecodeError:
                pass

        # 按逗号分割处理
        origins = [origin.strip() for origin in raw_value.split(",") if origin.strip()]
        return (
            origins if origins else ["http://localhost:3000", "http://localhost:8080"]
        )


class LLMSettings(BaseModel):
    """LLM相关配置 - 现在作为数据传输对象使用"""

    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    default_provider: LLMProvider = LLMProvider.OPENAI
    default_model: str = "gpt-4"
    max_retries: int = 3
    request_timeout: int = 60
    max_tokens: int = 4096


class DiscordMessageType(str, Enum):
    """Discord消息类型枚举"""
    TEXT = "text"           # 纯文本消息
    EMBED = "embed"         # 嵌入消息（如机器人发送的富文本）
    ATTACHMENT = "attachment"  # 包含附件的消息（图片、文件等）
    ALL = "all"            # 所有类型消息


class DiscordFilterConfig(BaseModel):
    """Discord消息过滤配置 - 优化版本，支持消息类型过滤"""

    # 全局过滤开关
    enabled: bool = Field(default=False, description="是否启用消息过滤")

    # 核心过滤条件 - 使用Set提高查找性能（O(1)时间复杂度）
    server_ids: Set[str] = Field(default_factory=set, description="允许的服务器ID集合")
    channel_ids: Set[str] = Field(default_factory=set, description="允许的频道ID集合")
    author_ids: Set[str] = Field(default_factory=set, description="允许的作者ID集合")

    # 消息类型过滤 - 新增功能
    allowed_message_types: Set[DiscordMessageType] = Field(
        default_factory=lambda: {DiscordMessageType.ALL},
        description="允许的消息类型集合"
    )

    @field_validator("server_ids", "channel_ids", "author_ids")
    @classmethod
    def validate_id_sets(cls, v):
        """验证ID集合格式并转换为set，提高查找性能"""
        if isinstance(v, set):
            return {str(id_).strip() for id_ in v if str(id_).strip().isdigit()}
        return set()

    @field_validator("allowed_message_types")
    @classmethod
    def validate_message_types(cls, v):
        """验证消息类型集合"""
        if isinstance(v, set):
            return {DiscordMessageType(item) for item in v if item in DiscordMessageType}
        return {DiscordMessageType.ALL}

    def is_empty(self) -> bool:
        """检查是否为空配置（无任何过滤条件）"""
        return not any([self.server_ids, self.channel_ids, self.author_ids])

    def should_filter_by_type(self) -> bool:
        """检查是否需要按消息类型过滤"""
        return DiscordMessageType.ALL not in self.allowed_message_types



class DiscordSettings(BaseModel):
    """Discord相关配置 - 现在作为数据传输对象使用"""

    token: Optional[str] = None
    channel_ids: List[str] = []

    # 新增配置选项
    auto_start: bool = Field(default=True, description="是否自动启动Discord监听器")
    reconnect_attempts: int = Field(default=5, description="最大重连尝试次数")
    reconnect_delay: int = Field(default=30, description="重连延迟（秒）")
    message_cache_size: int = Field(default=1000, description="消息缓存大小")
    deduplication_window: int = Field(default=5, description="消息去重时间窗口（分钟）")
    proxy: Optional[str] = Field(default=None, description="代理设置（支持http/https/socks5）")

    @field_validator("token")
    @classmethod
    def validate_token(cls, v):
        """验证Discord token格式"""
        if v and not v.startswith(("Bot ", "Bearer ")):
            # discord.py-self 使用用户token，不需要Bot前缀
            return v
        return v




class TradingSettings(BaseModel):
    """交易相关配置 - 现在作为数据传输对象使用"""

    default_exchange: str = "binance"
    simulation_mode: bool = False
    price_check_interval: int = 60


class Settings(BaseSettings):
    """应用总配置类 - 统一配置管理架构"""

    # 应用基础配置
    app_name: str = Field(default="AI Crypto Trading Agent", alias="APP_NAME")
    app_version: str = Field(default="1.0.0", alias="APP_VERSION")
    environment: str = Field(default="test", alias="ENVIRONMENT")
    debug: bool = Field(default=False, alias="DEBUG")
    log_level: LogLevel = Field(default=LogLevel.INFO, alias="LOG_LEVEL")

    # 日志配置
    log_to_file: bool = Field(default=True, alias="LOG_TO_FILE")
    log_file_path: str = Field(default="logs", alias="LOG_FILE_PATH")
    log_file_max_size: int = Field(default=10, alias="LOG_FILE_MAX_SIZE")  # MB
    log_file_backup_count: int = Field(default=5, alias="LOG_FILE_BACKUP_COUNT")
    log_retention_days: int = Field(default=7, alias="LOG_RETENTION_DAYS")

    # 数据库配置 - 直接在主配置类中定义
    # 自动检测Docker环境并使用适当的数据库主机
    def _get_default_database_url() -> str:
        import os

        if os.getenv("DOCKER_ENV") == "true" or os.path.exists("/.dockerenv"):
            # 在Docker容器内使用正确的postgres-test服务名
            return "postgresql+asyncpg://crypto_trader:test_password_123@postgres-test:5432/crypto_trader_test"
        else:
            # 在本地开发环境使用localhost
            return "postgresql+asyncpg://crypto_trader:test_password_123@localhost:5432/crypto_trader_test"

    database_url: str = Field(
        default_factory=_get_default_database_url,
        alias="DATABASE_URL",
    )
    db_pool_size: int = Field(default=10, alias="DB_POOL_SIZE")
    db_max_overflow: int = Field(default=20, alias="DB_MAX_OVERFLOW")
    db_pool_timeout: int = Field(default=30, alias="DB_POOL_TIMEOUT")
    db_pool_recycle: int = Field(default=1800, alias="DB_POOL_RECYCLE")
    db_echo: bool = Field(default=False, alias="DB_ECHO")

    # 安全配置
    secret_key: str = Field(
        default="development_secret_key_min_32_chars_here_change_in_production",
        alias="APP_SECRET_KEY",
    )
    jwt_secret_key: str = Field(
        default="development_jwt_secret_key_min_32_chars_here_change_in_production",
        alias="JWT_SECRET_KEY",
    )
    jwt_algorithm: str = Field(default="HS256", alias="JWT_ALGORITHM")
    jwt_expire_minutes: int = Field(default=30, alias="JWT_EXPIRE_MINUTES")
    password_hash_rounds: int = Field(default=12, alias="PASSWORD_HASH_ROUNDS")

    # API配置
    api_host: str = Field(default="0.0.0.0", alias="API_HOST")
    api_port: int = Field(default=8000, alias="API_PORT")
    api_prefix: str = Field(default="/api/v1", alias="API_PREFIX")
    api_url: str = Field(default="http://localhost:8000", alias="API_URL")
    cors_origins_raw: Optional[str] = Field(default=None, alias="CORS_ORIGINS")

    # LLM配置
    openai_api_key: Optional[str] = Field(default=None, alias="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, alias="ANTHROPIC_API_KEY")
    default_llm_provider: LLMProvider = Field(
        default=LLMProvider.OPENAI, alias="DEFAULT_LLM_PROVIDER"
    )
    default_llm_model: str = Field(default="gpt-4", alias="DEFAULT_LLM_MODEL")
    llm_max_retries: int = Field(default=3, alias="LLM_MAX_RETRIES")
    llm_request_timeout: int = Field(default=60, alias="LLM_REQUEST_TIMEOUT")
    llm_max_tokens: int = Field(default=4096, alias="LLM_MAX_TOKENS")

    # Discord配置
    discord_token: Optional[str] = Field(default=None, alias="DISCORD_TOKEN")
    discord_channel_ids_raw: Optional[str] = Field(
        default=None, alias="DISCORD_CHANNEL_IDS"
    )
    discord_filter_config_raw: Optional[str] = Field(
        default=None, alias="DISCORD_FILTER_CONFIG"
    )

    discord_auto_start: bool = Field(default=True, alias="DISCORD_AUTO_START")
    discord_reconnect_attempts: int = Field(
        default=5, alias="DISCORD_RECONNECT_ATTEMPTS"
    )
    discord_reconnect_delay: int = Field(default=30, alias="DISCORD_RECONNECT_DELAY")
    discord_message_cache_size: int = Field(
        default=1000, alias="DISCORD_MESSAGE_CACHE_SIZE"
    )
    discord_deduplication_window: int = Field(
        default=5, alias="DISCORD_DEDUPLICATION_WINDOW"
    )
    discord_proxy: Optional[str] = Field(default=None, alias="DISCORD_PROXY")

    # 交易配置
    default_exchange: str = Field(default="binance", alias="DEFAULT_EXCHANGE")
    simulation_mode: bool = Field(default=False, alias="SIMULATION_MODE")
    price_check_interval: int = Field(default=60, alias="PRICE_CHECK_INTERVAL")

    def __init__(self, **kwargs):
        """初始化配置，手动加载环境变量以确保正确读取"""
        # 手动加载.env文件
        import os

        from dotenv import load_dotenv

        # 动态获取项目根目录的.env文件路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
        env_file_path = os.path.join(project_root, ".env")

        # 尝试加载多个可能的环境文件
        env_files = [
            env_file_path,
            os.path.join(project_root, ".env.test"),
            ".env",
            ".env.test",
        ]

        for env_file in env_files:
            if os.path.exists(env_file):
                load_dotenv(env_file)
                break

        # 手动设置一些关键的环境变量到kwargs中，确保pydantic能正确读取
        if "cors_origins_raw" not in kwargs and os.getenv("CORS_ORIGINS"):
            kwargs["cors_origins_raw"] = os.getenv("CORS_ORIGINS")

        if "discord_channel_ids_raw" not in kwargs and os.getenv("DISCORD_CHANNEL_IDS"):
            kwargs["discord_channel_ids_raw"] = os.getenv("DISCORD_CHANNEL_IDS")

        # 调用父类初始化
        super().__init__(**kwargs)

    model_config = SettingsConfigDict(
        env_file=[".env", ".env.test"],  # 支持多个环境文件
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    # 加密用的盐值
    salt: bytes = b"crypto_trader_salt"

    @field_validator("secret_key")
    @classmethod
    def validate_secret_key(cls, v):
        """验证密钥长度"""
        if len(v) < 32:
            logger.warning("密钥长度不足32位，建议使用更长的密钥")
        if v.startswith("development_"):
            logger.warning("正在使用开发环境默认密钥，生产环境请更换")
        return v

    @field_validator("jwt_secret_key")
    @classmethod
    def validate_jwt_secret_key(cls, v):
        """验证JWT密钥长度"""
        if len(v) < 32:
            logger.warning("JWT密钥长度不足32位，建议使用更长的密钥")
        if v.startswith("development_"):
            logger.warning("正在使用开发环境默认JWT密钥，生产环境请更换")
        return v

    @field_validator("log_level", mode="before")
    @classmethod
    def parse_log_level(cls, v):
        """处理LOG_LEVEL的字符串形式"""
        if isinstance(v, str):
            try:
                return LogLevel(v.upper())
            except ValueError:
                logger.warning(f"无效的日志级别: {v}, 使用默认值: {LogLevel.INFO}")
                return LogLevel.INFO
        return v

    @field_validator("default_llm_provider", mode="before")
    @classmethod
    def validate_llm_provider(cls, v):
        """将字符串值转换为枚举"""
        if isinstance(v, str):
            try:
                return LLMProvider(v.lower())
            except ValueError:
                logger.warning(f"无效的LLM提供商: {v}, 使用默认值: {LLMProvider.OPENAI}")
                return LLMProvider.OPENAI
        return v

    @field_validator("simulation_mode", mode="before")
    @classmethod
    def parse_simulation_mode(cls, v):
        """处理SIMULATION_MODE的字符串形式"""
        if isinstance(v, str):
            return v.lower() == "true"
        return v

    @field_validator("db_echo", mode="before")
    @classmethod
    def parse_db_echo(cls, v):
        """处理DB_ECHO的字符串形式"""
        if isinstance(v, str):
            return v.lower() == "true"
        return v

    @property
    def cors_origins(self) -> List[str]:
        """动态解析CORS origins配置"""
        if not self.cors_origins_raw:
            return ["http://localhost:3000", "http://localhost:8080"]

        raw_value = self.cors_origins_raw.strip()
        if not raw_value:
            return ["http://localhost:3000", "http://localhost:8080"]

        # 尝试JSON解析（支持数组格式）
        if raw_value.startswith("[") and raw_value.endswith("]"):
            try:
                import json

                return json.loads(raw_value)
            except json.JSONDecodeError:
                pass

        # 按逗号分割处理
        origins = [origin.strip() for origin in raw_value.split(",") if origin.strip()]
        return (
            origins if origins else ["http://localhost:3000", "http://localhost:8080"]
        )

    @property
    def discord_channel_ids(self) -> List[str]:
        """动态解析Discord频道ID配置"""
        if not self.discord_channel_ids_raw:
            return []
        return [x.strip() for x in self.discord_channel_ids_raw.split(",") if x.strip()]

    @property
    def discord_filter_config(self) -> DiscordFilterConfig:
        """动态解析Discord过滤配置"""
        if not self.discord_filter_config_raw:
            # 返回默认配置（禁用所有过滤）
            return DiscordFilterConfig()

        try:
            import json
            config_dict = json.loads(self.discord_filter_config_raw)
            return DiscordFilterConfig(**config_dict)
        except (json.JSONDecodeError, TypeError, ValueError) as e:
            logger.warning(f"解析Discord过滤配置失败: {e}，使用默认配置")
            return DiscordFilterConfig()

    # 兼容性属性 - 提供子配置对象的访问接口
    @property
    def db(self) -> "DatabaseSettings":
        """数据库配置兼容性接口"""
        return DatabaseSettings(
            url=self.database_url,
            pool_size=self.db_pool_size,
            max_overflow=self.db_max_overflow,
            pool_timeout=self.db_pool_timeout,
            pool_recycle=self.db_pool_recycle,
            echo=self.db_echo,
        )

    @property
    def security(self) -> "SecuritySettings":
        """安全配置兼容性接口"""
        return SecuritySettings(
            secret_key=self.secret_key,
            jwt_secret_key=self.jwt_secret_key,
            jwt_algorithm=self.jwt_algorithm,
            jwt_expire_minutes=self.jwt_expire_minutes,
            password_hash_rounds=self.password_hash_rounds,
        )

    @property
    def api(self) -> "APISettings":
        """API配置兼容性接口"""
        return APISettings(
            host=self.api_host,
            port=self.api_port,
            prefix=self.api_prefix,
            url=self.api_url,
            cors_origins_raw=self.cors_origins_raw,
        )

    @property
    def llm(self) -> "LLMSettings":
        """LLM配置兼容性接口"""
        return LLMSettings(
            openai_api_key=self.openai_api_key,
            anthropic_api_key=self.anthropic_api_key,
            default_provider=self.default_llm_provider,
            default_model=self.default_llm_model,
            max_retries=self.llm_max_retries,
            request_timeout=self.llm_request_timeout,
            max_tokens=self.llm_max_tokens,
        )

    @property
    def discord(self) -> "DiscordSettings":
        """Discord配置兼容性接口"""
        return DiscordSettings(
            token=self.discord_token,
            channel_ids=self.discord_channel_ids,
            auto_start=self.discord_auto_start,
            reconnect_attempts=self.discord_reconnect_attempts,
            reconnect_delay=self.discord_reconnect_delay,
            message_cache_size=self.discord_message_cache_size,
            deduplication_window=self.discord_deduplication_window,
            proxy=self.discord_proxy,
        )

    @property
    def trading(self) -> "TradingSettings":
        """交易配置兼容性接口"""
        return TradingSettings(
            default_exchange=self.default_exchange,
            simulation_mode=self.simulation_mode,
            price_check_interval=self.price_check_interval,
        )

    @property
    def jwt(self) -> "JWTSettings":
        """JWT配置兼容性接口"""
        return JWTSettings(
            secret_key=self.jwt_secret_key,
            algorithm=self.jwt_algorithm,
            access_token_expire_minutes=self.jwt_expire_minutes,
            refresh_token_expire_days=7,
        )


@lru_cache()
def get_settings() -> Settings:
    """
    获取应用配置实例，使用lru_cache减少重复加载

    Returns:
        Settings: 配置实例
    """
    try:
        settings = Settings()
        logger.info(
            "加载配置完成",
            app_name=settings.app_name,
            debug=settings.debug,
            log_level=settings.log_level,
        )
        return settings
    except Exception as e:
        logger.error(f"加载配置失败: {str(e)}")
        raise


# 全局配置实例
settings = get_settings()
