<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监控页面修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button.success { background-color: #28a745; }
        button.success:hover { background-color: #218838; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>监控页面修复验证工具</h1>
        <p>这个工具验证监控页面数据显示问题的修复效果。</p>
        
        <div class="test-section">
            <h3>修复步骤总结</h3>
            <div class="step">
                <strong>步骤1:</strong> 发现问题 - 前端需要认证才能访问监控API
            </div>
            <div class="step">
                <strong>步骤2:</strong> 发现API响应处理问题 - API客户端提取了data字段，但store期望完整响应
            </div>
            <div class="step">
                <strong>步骤3:</strong> 修复API客户端 - 返回完整的API响应对象而不是只返回data字段
            </div>
            <div class="step">
                <strong>步骤4:</strong> 验证修复效果 - 测试完整的数据流
            </div>
        </div>
        
        <div class="test-section">
            <h3>1. 认证测试</h3>
            <div id="authTestStatus" class="status info">点击按钮开始认证测试</div>
            <button onclick="testAuthentication()">测试认证</button>
        </div>
        
        <div class="test-section">
            <h3>2. API响应格式测试</h3>
            <div id="apiFormatStatus" class="status info">点击按钮测试API响应格式</div>
            <button onclick="testAPIResponseFormat()">测试API响应格式</button>
        </div>
        
        <div class="test-section">
            <h3>3. 监控数据流测试</h3>
            <div id="dataFlowStatus" class="status info">点击按钮测试完整数据流</div>
            <button onclick="testMonitoringDataFlow()">测试监控数据流</button>
        </div>
        
        <div class="test-section">
            <h3>4. 前端页面测试</h3>
            <div id="frontendStatus" class="status info">点击按钮打开前端页面</div>
            <button onclick="openMonitoringPage()">打开监控页面</button>
            <button onclick="openLoginPage()">打开登录页面</button>
        </div>
        
        <div id="results" class="container" style="display: none;">
            <h3>测试结果</h3>
            <pre id="resultDetails"></pre>
        </div>
    </div>

    <script>
        let authToken = null;
        
        // 测试认证
        async function testAuthentication() {
            const status = document.getElementById('authTestStatus');
            status.className = 'status info';
            status.innerHTML = '🔄 正在测试认证...';
            
            try {
                // 1. 测试登录
                const loginResponse = await fetch('http://localhost:8000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'username=demo&password=password123'
                });
                
                if (!loginResponse.ok) {
                    throw new Error(`登录失败: ${loginResponse.status}`);
                }
                
                const loginData = await loginResponse.json();
                authToken = loginData.access_token;
                
                // 2. 测试token验证
                const meResponse = await fetch('http://localhost:8000/api/v1/auth/me', {
                    headers: {
                        'Authorization': 'Bearer ' + authToken,
                        'Accept': 'application/json'
                    }
                });
                
                if (!meResponse.ok) {
                    throw new Error(`Token验证失败: ${meResponse.status}`);
                }
                
                const userData = await meResponse.json();
                
                status.className = 'status success';
                status.innerHTML = '✅ 认证测试通过 - 用户: ' + userData.username;
                
                showResult({
                    authTest: 'success',
                    token: authToken.substring(0, 50) + '...',
                    user: userData
                });
                
            } catch (error) {
                status.className = 'status error';
                status.innerHTML = '❌ 认证测试失败: ' + error.message;
                showResult({ authTest: 'failed', error: error.message });
            }
        }
        
        // 测试API响应格式
        async function testAPIResponseFormat() {
            const status = document.getElementById('apiFormatStatus');
            status.className = 'status info';
            status.innerHTML = '🔄 正在测试API响应格式...';
            
            if (!authToken) {
                status.className = 'status warning';
                status.innerHTML = '⚠️ 请先进行认证测试';
                return;
            }
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/monitoring/kpi?hours=24', {
                    headers: {
                        'Authorization': 'Bearer ' + authToken,
                        'Accept': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status}`);
                }
                
                const data = await response.json();
                
                // 检查响应格式
                const hasSuccess = 'success' in data;
                const hasData = 'data' in data;
                const hasMessage = 'message' in data;
                
                if (hasSuccess && hasData) {
                    status.className = 'status success';
                    status.innerHTML = '✅ API响应格式正确 - 包含success和data字段';
                } else {
                    status.className = 'status warning';
                    status.innerHTML = '⚠️ API响应格式异常 - 缺少必要字段';
                }
                
                showResult({
                    apiFormatTest: hasSuccess && hasData ? 'success' : 'warning',
                    responseStructure: {
                        hasSuccess,
                        hasData,
                        hasMessage,
                        dataKeys: Object.keys(data.data || {}),
                        fullResponse: data
                    }
                });
                
            } catch (error) {
                status.className = 'status error';
                status.innerHTML = '❌ API响应格式测试失败: ' + error.message;
                showResult({ apiFormatTest: 'failed', error: error.message });
            }
        }
        
        // 测试监控数据流
        async function testMonitoringDataFlow() {
            const status = document.getElementById('dataFlowStatus');
            status.className = 'status info';
            status.innerHTML = '🔄 正在测试监控数据流...';
            
            if (!authToken) {
                status.className = 'status warning';
                status.innerHTML = '⚠️ 请先进行认证测试';
                return;
            }
            
            try {
                // 模拟前端监控store的数据获取流程
                const endpoints = [
                    { name: 'KPI指标', url: '/api/v1/monitoring/kpi?hours=24' },
                    { name: '流程状态', url: '/api/v1/monitoring/flow-status' },
                    { name: '最近信号', url: '/api/v1/monitoring/recent-signals?limit=10' },
                    { name: '活跃告警', url: '/api/v1/monitoring/alerts' }
                ];
                
                const results = [];
                
                for (const endpoint of endpoints) {
                    try {
                        const response = await fetch(`http://localhost:8000${endpoint.url}`, {
                            headers: {
                                'Authorization': 'Bearer ' + authToken,
                                'Accept': 'application/json'
                            }
                        });
                        
                        if (!response.ok) {
                            throw new Error(`${response.status}: ${response.statusText}`);
                        }
                        
                        const data = await response.json();
                        
                        results.push({
                            endpoint: endpoint.name,
                            success: data.success || false,
                            hasData: !!data.data,
                            dataType: typeof data.data,
                            dataSize: Array.isArray(data.data) ? data.data.length : 
                                     (typeof data.data === 'object' ? Object.keys(data.data).length : 1)
                        });
                        
                    } catch (error) {
                        results.push({
                            endpoint: endpoint.name,
                            success: false,
                            error: error.message
                        });
                    }
                }
                
                const successCount = results.filter(r => r.success).length;
                const totalCount = results.length;
                
                if (successCount === totalCount) {
                    status.className = 'status success';
                    status.innerHTML = `✅ 监控数据流测试通过 (${successCount}/${totalCount})`;
                } else {
                    status.className = 'status warning';
                    status.innerHTML = `⚠️ 部分监控数据流测试失败 (${successCount}/${totalCount})`;
                }
                
                showResult({
                    dataFlowTest: successCount === totalCount ? 'success' : 'partial',
                    results: results,
                    summary: {
                        total: totalCount,
                        success: successCount,
                        failed: totalCount - successCount
                    }
                });
                
            } catch (error) {
                status.className = 'status error';
                status.innerHTML = '❌ 监控数据流测试失败: ' + error.message;
                showResult({ dataFlowTest: 'failed', error: error.message });
            }
        }
        
        // 打开监控页面
        function openMonitoringPage() {
            const status = document.getElementById('frontendStatus');
            status.className = 'status info';
            status.innerHTML = '🔄 正在打开监控页面...';
            
            window.open('http://localhost:5173/monitoring', '_blank');
            
            status.className = 'status success';
            status.innerHTML = '✅ 监控页面已在新标签页中打开';
        }
        
        // 打开登录页面
        function openLoginPage() {
            window.open('http://localhost:5173/login', '_blank');
        }
        
        // 显示结果
        function showResult(result) {
            const resultsDiv = document.getElementById('results');
            const resultDetails = document.getElementById('resultDetails');
            
            resultDetails.textContent = JSON.stringify(result, null, 2);
            resultsDiv.style.display = 'block';
            
            // 滚动到结果区域
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 页面加载时的说明
        window.onload = function() {
            console.log('监控页面修复验证工具已加载');
            console.log('请按顺序执行测试：1. 认证测试 -> 2. API响应格式测试 -> 3. 监控数据流测试 -> 4. 前端页面测试');
        };
    </script>
</body>
</html>
