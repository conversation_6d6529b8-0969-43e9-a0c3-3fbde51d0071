<template>
  <v-card 
    class="kpi-card" 
    :class="cardClass"
    elevation="2"
    hover
  >
    <v-card-text class="d-flex align-center pa-4">
      <div class="flex-grow-1">
        <!-- KPI数值 -->
        <div 
          class="kpi-value text-h4 font-weight-bold mb-1" 
          :style="{ color: valueColor }"
        >
          {{ formattedValue }}
        </div>
        
        <!-- KPI标题 -->
        <div class="kpi-title text-body-1 text-medium-emphasis mb-2">
          {{ title }}
        </div>
        
        <!-- 趋势指示器 -->
        <div 
          v-if="trend && trend.text" 
          class="kpi-trend d-flex align-center"
          :class="trendClass"
        >
          <v-icon 
            size="small" 
            class="mr-1"
            :icon="trendIcon"
          />
          <span class="text-caption">{{ trend.text }}</span>
        </div>
      </div>
      
      <!-- 图标 -->
      <v-avatar 
        :color="color" 
        variant="tonal" 
        size="56"
        class="ml-4"
      >
        <v-icon 
          :icon="icon" 
          size="28"
        />
      </v-avatar>
    </v-card-text>
    
    <!-- 加载状态覆盖层 -->
    <v-overlay
      v-if="loading"
      contained
      class="d-flex align-center justify-center"
    >
      <v-progress-circular
        indeterminate
        size="24"
        color="primary"
      />
    </v-overlay>
  </v-card>
</template>

<script setup>
import { computed } from 'vue'
import { monitoringUtils } from '@/api/monitoring'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    required: true
  },
  trend: {
    type: Object,
    default: () => null
    // { direction: 'up'|'down'|'stable', value: number, text: string }
  },
  color: {
    type: String,
    default: 'primary'
  },
  icon: {
    type: String,
    required: true
  },
  format: {
    type: String,
    default: 'number',
    validator: (value) => ['percentage', 'duration', 'number'].includes(value)
  },
  loading: {
    type: Boolean,
    default: false
  },
  clickable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

// 格式化数值显示
const formattedValue = computed(() => {
  const value = props.value

  // 处理 undefined、null 值
  if (value === undefined || value === null) {
    return '0'
  }

  switch (props.format) {
    case 'percentage':
      return monitoringUtils.formatPercentage(value)
    case 'duration':
      return monitoringUtils.formatDuration(value)
    case 'number':
      return monitoringUtils.formatNumber(value)
    default:
      return String(value)
  }
})

// 数值颜色
const valueColor = computed(() => {
  // 根据趋势和类型确定颜色
  if (props.trend) {
    const { direction } = props.trend
    
    // 对于成功率，上升是好的
    if (props.title.includes('成功率') || props.title.includes('Success Rate')) {
      if (direction === 'up') return '#4CAF50' // 绿色
      if (direction === 'down') return '#F44336' // 红色
    }
    
    // 对于处理时间，下降是好的
    if (props.title.includes('处理时间') || props.title.includes('Processing Time')) {
      if (direction === 'down') return '#4CAF50' // 绿色
      if (direction === 'up') return '#F44336' // 红色
    }
    
    // 对于异常数量，数值为0是好的
    if (props.title.includes('异常') || props.title.includes('卡住') || props.title.includes('Stuck')) {
      return props.value === 0 ? '#4CAF50' : '#F44336'
    }
  }
  
  // 默认使用主题色
  return 'rgb(var(--v-theme-on-surface))'
})

// 趋势样式类
const trendClass = computed(() => {
  if (!props.trend) return ''
  
  const { direction } = props.trend
  
  // 根据指标类型和趋势方向确定样式
  if (props.title.includes('成功率') || props.title.includes('Success Rate')) {
    return {
      'text-success': direction === 'up',
      'text-error': direction === 'down',
      'text-medium-emphasis': direction === 'stable'
    }
  }
  
  if (props.title.includes('处理时间') || props.title.includes('Processing Time')) {
    return {
      'text-success': direction === 'down',
      'text-error': direction === 'up',
      'text-medium-emphasis': direction === 'stable'
    }
  }
  
  // 默认样式
  return {
    'text-success': direction === 'up',
    'text-error': direction === 'down',
    'text-medium-emphasis': direction === 'stable'
  }
})

// 趋势图标
const trendIcon = computed(() => {
  if (!props.trend) return ''
  
  switch (props.trend.direction) {
    case 'up':
      return 'mdi-trending-up'
    case 'down':
      return 'mdi-trending-down'
    case 'stable':
    default:
      return 'mdi-trending-neutral'
  }
})

// 卡片样式类
const cardClass = computed(() => ({
  'kpi-card--clickable': props.clickable,
  'kpi-card--loading': props.loading
}))

// 处理点击事件
const handleClick = () => {
  if (props.clickable && !props.loading) {
    emit('click')
  }
}
</script>

<style scoped>
.kpi-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.kpi-card--clickable {
  cursor: pointer;
}

.kpi-card--clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.kpi-card--loading {
  opacity: 0.7;
}

.kpi-value {
  line-height: 1.2;
}

.kpi-title {
  line-height: 1.3;
}

.kpi-trend {
  line-height: 1.2;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .kpi-card .v-card-text {
    padding: 12px !important;
  }
  
  .kpi-value {
    font-size: 1.5rem !important;
  }
  
  .v-avatar {
    width: 48px !important;
    height: 48px !important;
  }
  
  .v-avatar .v-icon {
    font-size: 24px !important;
  }
}

/* 深色主题适配 */
.v-theme--dark .kpi-card {
  background-color: rgb(var(--v-theme-surface));
}

.v-theme--dark .kpi-card:hover {
  background-color: rgb(var(--v-theme-surface-variant));
}

/* 动画效果 */
.kpi-value,
.kpi-trend {
  transition: all 0.3s ease;
}

/* 趋势指示器动画 */
.kpi-trend .v-icon {
  transition: transform 0.3s ease;
}

.kpi-trend:hover .v-icon {
  transform: scale(1.1);
}

/* 加载状态样式 */
.v-overlay {
  background-color: rgba(255, 255, 255, 0.8);
}

.v-theme--dark .v-overlay {
  background-color: rgba(0, 0, 0, 0.8);
}
</style>
