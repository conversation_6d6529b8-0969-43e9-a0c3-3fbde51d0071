# 📋 测试文档体系总览

**版本**: 2.0 | **更新**: 2025-07-26 | **项目**: AI Crypto Trading Agent

## 🎯 核心原则

### 真实数据原则 (强制)
- **API/E2E 测试**: 必须使用真实后端数据，严禁 mock
- **数据一致性**: 确保前后端实时同步

### 分层测试策略
```
E2E (Playwright) → API (Playwright) → 组件 (Vitest) → 单元 (pytest) → 数据库 (pytest)
```

### 执行流程
1. Docker Compose 启动服务 2. 分层测试执行 3. 后端更新需重启服务

## 📖 文档清单

### 核心文档
| 文档 | 用途 | 关键内容 |
|------|------|----------|
| **[测试规范](./项目测试规范.md)** | 编写标准 | 思考清单、编码规范、执行步骤 |
| **[测试设计文档](./测试设计文档.md)** | 架构策略 | 分层测试架构、责任矩阵、集成点设计 |
| **[测试清单](./项目测试清单.md)** | 覆盖指导 | 模块清单、优先级(P0/P1/P2)、实现状态、AI指导 |
| **[测试经验](./项目测试经验.md)** | 问题解决 | 常见失败、排查指南、调试技巧 |
| **[测试配置](./项目测试配置.md)** | 环境设置 | 配置文件、参数说明、环境变量 |

### 补充说明
所有测试相关内容已整合到上述核心文档中：
- **测试清单**：包含测试覆盖指导、实现状态跟踪和AI生成指导
- **测试经验**：包含快速诊断指南和详细问题解决方案

## 🚀 快速开始

### 环境准备
```bash
python scripts/test.py --status                 # 检查状态
docker-compose -f docker-compose.test.yml up -d postgres-test  # 启动数据库服务
cd backend && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload  # 后端服务
cd frontend && npm run dev                       # 前端服务
```

### 分步测试执行 (强制流程)
```bash
# 1. 查看测试清单
python scripts/test.py --all

# 2. 按步骤执行 (任何失败必须立即修复)
python scripts/test.py --pytest unit            # Step 1: 后端单元
python scripts/test.py --pytest integration     # Step 2: 后端集成
python scripts/test.py --vitest unit            # Step 3: 前端单元
python scripts/test.py --playwright api         # Step 4: API测试
python scripts/test.py --playwright e2e         # Step 5: E2E测试

# 3. 最终验证
python scripts/test.py --execute-all
```

### 查看结果
```bash
npm run test:e2e:report                              # 并行测试报告 (推荐)
open ../temp/frontend/playwright/parallel-report/index.html  # 并行E2E报告
open ../temp/backend/coverage/html/index.html       # 覆盖率
```

## 🎯 AI 测试生成指导

### 使用流程
1. **需求分析** → 确定测试范围
2. **参考清单** → 使用 [测试清单](./项目测试清单.md) 确定类型和优先级
3. **遵循规范** → 按照 [测试规范](./项目测试规范.md) 编写代码
4. **配置环境** → 参考 [测试配置](./项目测试配置.md) 设置环境
5. **问题解决** → 使用 [测试经验](./项目测试经验.md) 解决问题

### 生成原则
- **真实数据**: API/E2E 测试必须使用真实后端数据
- **分层覆盖**: 在合适层级验证功能
- **完整性**: 正常流程 + 异常处理 + 边界条件
- **可维护性**: 清晰代码 + 完整注释

### 质量标准
- **覆盖率**: API 100% | 核心业务 90% | UI组件 70%
- **性能**: 单元<5min | 集成<10min | E2E<15min
- **稳定性**: 通过率≥95% | 支持并发

## 📊 技术栈
- **后端**: pytest + PostgreSQL + FastAPI
- **前端单元**: Vitest + Vue Test Utils + jsdom
- **前端集成**: Playwright + 真实后端API
- **E2E**: Playwright + 完整应用栈

## 🔄 维护原则
- **每周**: 检查失败测试，更新数据
- **每月**: 审查覆盖率，清理过时测试
- **每季度**: 评估架构，优化性能
- **变更时**: 同步更新相关文档

## 📞 支持
- 测试问题 → [测试经验](./项目测试经验.md)
- 配置问题 → [测试配置](./项目测试配置.md)
- 复杂问题 → 联系开发团队

---
**维护**: AI Crypto Trading Team | **更新**: 2025-07-26 | **版本**: v2.0
