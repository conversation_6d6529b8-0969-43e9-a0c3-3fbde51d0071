# 测试部署运维文档

## 目录
1. [混合开发环境架构](#1-混合开发环境架构)
2. [生产环境部署策略](#2-生产环境部署策略)
3. [配置与迁移](#3-配置与迁移)
4. [运维与监控](#4-运维与监控)
5. [配置验证脚本](#5-配置验证脚本)
6. [技术栈](#6-技术栈)

---

# 1. 混合开发环境架构

## 1.1 概述

本项目采用混合开发环境架构，解决了 Docker 容器重启导致的开发中断问题。新架构仅在 Docker 中运行 PostgreSQL 数据库，而后端和前端服务在本地运行，提供了更稳定、高效的开发体验。

**架构特点**:
- **PostgreSQL**: Docker 容器运行，提供稳定的数据库服务
- **后端服务**: 本地运行，避免容器重启问题，支持快速开发迭代
- **前端服务**: 本地运行，享受原生的热重载和调试体验

**详细设置指南请参考**: [4. 开发测试环境](./4.%20开发测试环境.md)

## 1.2 混合环境服务配置

### PostgreSQL 数据库服务 (Docker)
- **容器名**: `crypto_trader_postgres_test`
- **端口**: `5432:5432`
- **环境变量**:
  - `POSTGRES_DB=crypto_trader_test`
  - `POSTGRES_USER=crypto_trader`
  - `POSTGRES_PASSWORD=test_password_123`
- **健康检查**: 自动检测数据库连接状态
- **数据持久化**: 使用Docker卷 `postgres_test_data`
- **访问方式**: 本地服务通过 `localhost:5432` 连接

### 后端服务 (本地运行)
- **运行方式**: 本地 Python 环境
- **端口**: `8000`
- **启动命令**: `uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload`
- **数据库连接**: `postgresql+asyncpg://crypto_trader:test_password_123@localhost:5432/crypto_trader_test`
- **热重载**: uvicorn 自动重启
- **健康检查**: HTTP端点检查 `/api/v1/system/health`

### 前端服务 (本地运行)
- **运行方式**: 本地 Node.js 环境
- **端口**: `5173`
- **启动命令**: `npm run dev`
- **API连接**: `http://localhost:8000`
- **热重载**: Vite HMR (热模块替换)
- **开发模式**: 支持实时预览和调试

## 1.3 混合环境配置文件

### docker-compose.test.yml
混合开发环境的Docker Compose配置文件，仅定义PostgreSQL数据库服务。

```yaml
services:
  # PostgreSQL数据库服务
  postgres-test:
    image: postgres:15-alpine
    container_name: crypto_trader_postgres_test
    environment:
      # 数据库配置
      POSTGRES_DB: crypto_trader_test
      POSTGRES_USER: crypto_trader
      POSTGRES_PASSWORD: test_password_123
      # PostgreSQL配置
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      # 数据持久化
      - postgres_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U crypto_trader -d crypto_trader_test"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - crypto_trader_test

  # 后端和前端服务已注释，在本地运行
  # 详细配置请参考 docs/4. 开发测试环境.md
```

### 本地环境变量配置

#### 后端 .env.local
```bash
DATABASE_URL=postgresql+asyncpg://crypto_trader:test_password_123@localhost:5432/crypto_trader_test
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=http://localhost:5173,http://localhost:3000
DEBUG=true
LOG_LEVEL=DEBUG
```

#### 前端 .env.local
```bash
VITE_API_BASE_URL=http://localhost:8000
VITE_HOST=0.0.0.0
VITE_PORT=5173
NODE_ENV=development
```

## 1.4 热重载功能

### 后端热重载 (本地)
- **技术**: FastAPI + uvicorn `--reload` 模式
- **监控范围**: `backend/` 目录下所有Python文件
- **重启触发**: 文件保存时自动重启服务器
- **验证方法**: 修改API端点，观察终端中的重启信息
- **优势**: 比Docker容器重启更快，调试体验更好

### 前端热重载 (本地)
- **技术**: Vite HMR (热模块替换)
- **监控范围**: `frontend/src/` 目录下所有前端文件
- **更新方式**: 无需重启，实时更新浏览器内容
- **验证方法**: 修改Vue组件，观察浏览器实时更新
- **优势**: 原生文件监听，响应更快，更稳定

## 1.5 混合环境服务管理

### ServiceManager 适配
ServiceManager类已适配混合开发环境：

```python
class ServiceManager:
    def __init__(self, compose_file: str = "docker-compose.test.yml"):
        self.compose_file = compose_file
        self.config = Config()

    async def is_service_healthy(self, service_name: str) -> bool:
        """检查服务健康状态 - 支持Docker和本地服务"""

    async def start_database_service(self) -> bool:
        """启动PostgreSQL数据库服务"""

    async def check_local_service(self, port: int) -> bool:
        """检查本地服务端口可用性"""
```

### 主要变更
- 数据库服务通过Docker Compose管理
- 后端和前端服务通过本地进程管理
- 新增本地服务健康检查方法
- 保持向后兼容性

## 1.6 混合环境使用方法

### 启动开发环境
```bash
# 1. 启动数据库服务
docker-compose -f docker-compose.test.yml up -d postgres-test

# 2. 启动后端服务 (新终端)
cd backend
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 3. 启动前端服务 (新终端)
cd frontend
npm run dev

# 查看数据库状态
docker-compose -f docker-compose.test.yml ps
```

### 运行测试
```bash
# 检查服务状态
python scripts/test.py --status

# 运行后端单元测试
cd backend && pytest tests/unit/

# 运行前端单元测试
cd frontend && npm run test:unit

# 运行API集成测试
cd frontend && npm run test:api

# 运行所有测试
python scripts/test.py --all
```

### 开发调试
```bash
# 查看数据库日志
docker logs crypto_trader_postgres_test --tail 20

# 后端调试 - 查看终端输出
# 前端调试 - 使用浏览器开发者工具

# 连接数据库调试
docker exec -it crypto_trader_postgres_test psql -U crypto_trader -d crypto_trader_test
```

### 停止服务
```bash
# 停止本地服务 (Ctrl+C 在对应终端)

# 停止数据库服务
docker-compose -f docker-compose.test.yml stop postgres-test

# 完全清理 (包括数据)
docker-compose -f docker-compose.test.yml down -v
```

## 1.7 混合环境优势

### 稳定性
- 避免Docker容器重启导致的开发中断
- PostgreSQL在Docker中稳定运行，数据持久化
- 本地服务启动快速，调试方便

### 性能
- 本地服务响应速度更快
- 原生文件监听，热重载更及时
- 减少Docker网络开销

### 开发体验
- 更好的调试体验和错误追踪
- IDE集成更完善
- 日志输出更直观

### 灵活性
- 可以独立重启各个服务
- 支持不同的开发工具和配置
- 便于性能分析和优化

### 兼容性
- 保持与现有开发流程的兼容
- 支持回退到完整Docker环境
- CI/CD流程无需大幅修改

## 1.8 混合环境故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 检查端口使用情况
   lsof -i :5432 -i :8000 -i :5173

   # 杀死占用端口的进程
   kill -9 <PID>

   # 停止数据库服务
   docker-compose -f docker-compose.test.yml stop postgres-test
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库容器状态
   docker ps | grep postgres

   # 查看数据库日志
   docker logs crypto_trader_postgres_test

   # 重启数据库
   docker-compose -f docker-compose.test.yml restart postgres-test

   # 测试连接
   docker exec -it crypto_trader_postgres_test psql -U crypto_trader -d crypto_trader_test -c "SELECT 1;"
   ```

3. **后端启动失败**
   ```bash
   # 检查Python环境
   which python
   python --version

   # 检查依赖
   pip list | grep fastapi

   # 重新安装依赖
   cd backend && pip install -r requirements.txt

   # 检查环境变量
   echo $DATABASE_URL
   ```

4. **前端热重载失效**

   **问题症状**: 修改前端代码后浏览器不自动刷新

   **解决方案**:
   ```bash
   # 1. 检查Vite进程
   ps aux | grep vite

   # 2. 重启前端服务
   cd frontend
   npm run dev

   # 3. 清理缓存
   rm -rf .vite node_modules/.vite
   npm run dev
   ```

   **关键配置要点**:
   - 确保使用本地Node.js环境
   - 检查文件监听权限
   - 验证端口5173未被占用

## 1.9 迁移指南

### 从完整Docker Compose迁移到混合环境

1. **停止现有服务**
   ```bash
   docker-compose -f docker-compose.test.yml down
   ```

2. **更新配置文件**: 使用新的`docker-compose.test.yml`（仅包含PostgreSQL）

3. **创建本地环境变量文件**
   - 后端: 创建`.env.local`
   - 前端: 创建`.env.local`

4. **启动混合环境**
   ```bash
   # 启动数据库
   docker-compose -f docker-compose.test.yml up -d postgres-test

   # 启动本地服务
   cd backend && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
   cd frontend && npm run dev &
   ```

### 回退到完整Docker环境

如需回退，可以恢复原始的docker-compose.test.yml配置文件。

**详细迁移指南请参考**: [4. 开发测试环境 - 迁移指南](./4.%20开发测试环境.md#11-迁移指南)

---

# 2. 生产环境部署策略

## 2.1 容器化部署

- **技术**: **Docker** & **Docker Compose**
- **实现**: 提供一个 `docker-compose.yml` 文件，定义 `backend`, `frontend` (Nginx), 两个服务，实现一键启动
- **构建优化**: 前后端均采用多阶段构建的 `Dockerfile`，以优化最终镜像的大小和安全性
- **配置管理**: 所有敏感信息和环境相关配置**必须**通过 `.env` 文件和环境变量进行管理

## 2.2 生产环境后端 Dockerfile

```dockerfile
# 多阶段构建 - 后端
FROM python:3.11-slim as builder

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# 生产阶段
FROM python:3.11-slim

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app

# 复制依赖
COPY --from=builder /root/.local /home/<USER>/.local

# 设置环境变量
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app

# 切换到app用户
USER app
WORKDIR /app

# 复制应用代码
COPY --chown=app:app . .

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动应用
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 2.3 生产环境前端 Dockerfile

```dockerfile
# 多阶段构建 - 前端
FROM node:18-alpine as builder

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

## 2.4 生产环境 Docker Compose 配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=postgresql+asyncpg://${DB_USER:-postgres}:${DB_PASSWORD:-password}@postgres:5432/${DB_NAME:-crypto_trader}
      - APP_SECRET_KEY=${APP_SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENVIRONMENT=${ENVIRONMENT:-production}
    volumes:
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 5s
      retries: 3
    restart: unless-stopped

networks:
  default:
    driver: bridge
```

---

# 3. 配置与迁移

## 3.1 配置管理

项目采用基于 **Pydantic Settings** 的分层配置策略，实现了类型安全、自动验证和环境感知的配置管理系统。

### 3.1.1 配置层次结构

配置系统按优先级从高到低的加载顺序：

1. **环境变量**：运行时动态配置，优先级最高
2. **`.env` 文件**：环境配置，支持多环境文件（`.env.test`, `.env.production`）
3. **配置文件**：业务逻辑配置（风险参数、交易对白名单等）
4. **代码默认值**：合理的默认配置，优先级最低

### 3.1.2 环境变量模板

`.env.example`：

```bash
# .env.example
# ===========================================
# AI Crypto Trading Agent - 环境配置模板
# ===========================================

# 应用基础配置
APP_NAME="AI Crypto Trading Agent"
APP_VERSION="1.0.0"
ENVIRONMENT="production"  # test, staging, production
DEBUG="false"
LOG_LEVEL="INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# 安全配置（生产环境必须修改）
APP_SECRET_KEY="your-32-character-secret-key-here"
JWT_SECRET_KEY="your-32-character-jwt-secret-here"
JWT_EXPIRE_MINUTES="30"

# 数据库配置
DB_NAME="crypto_trader"
DB_USER="postgres"
DB_PASSWORD="your-secure-password-here"
DB_HOST="postgres"
DB_PORT="5432"
DATABASE_URL="postgresql+asyncpg://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

# API配置
API_HOST="0.0.0.0"
API_PORT="8000"
API_PREFIX="/api/v1"
CORS_ORIGINS="http://localhost:3000,https://yourdomain.com"

# LLM配置
OPENAI_API_KEY="your-openai-api-key-here"
OPENAI_MODEL="gpt-4"
ANTHROPIC_API_KEY="your-anthropic-api-key-here"

# Discord配置
DISCORD_BOT_TOKEN="your-discord-bot-token-here"
DISCORD_CHANNEL_IDS="channel1,channel2,channel3"

# 交易所配置（示例，实际配置在数据库中）
DEFAULT_EXCHANGE="binance"
SANDBOX_MODE="true"

# 风控配置
MAX_CONCURRENT_ORDERS="10"
DEFAULT_RISK_PERCENTAGE="0.02"
SUPPORTED_SYMBOLS="BTC/USDT,ETH/USDT,BNB/USDT"

# 监控配置
ENABLE_METRICS="true"
METRICS_PORT="9090"
LOKI_URL="http://loki:3100"

# 邮件通知配置（可选）
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
NOTIFICATION_EMAIL="<EMAIL>"
```

## 3.2 数据库迁移 (Database Migration)

- **工具**: **Alembic**。作为 SQLAlchemy 的官方迁移工具，与数据模型紧密集成。
- **工作流**: 数据库结构（Schema）的任何变更都通过 Alembic 生成版本化的迁移脚本，并在部署流程中自动执行，确保部署的可靠性和可重复性。

---

# 4. 运维与监控

## 4.1 可观测性 (Observability)

- **结构化日志 (Structured Logging)**: 所有日志输出（使用 `structlog` 库）必须是 JSON 格式，并包含 `task_id`、`user_id`、`signal_id` 等上下文信息，便于在日志聚合平台（如 Loki）或 `docker-compose logs` 中进行筛选和分析。
- **健康检查端点**: 提供 `/health` 和 `/health/detailed` 端点，用于监控数据库、LLM API 等核心依赖的健康状况。
- **信号功能监控**: 监控信号接收率、AI解析成功率、处理延迟等关键指标，确保信号功能的稳定性。

### 4.1.1 结构化日志配置

```python
# app/core/logging.py
import structlog
import logging
from typing import Any, Dict
import json
from datetime import datetime

def setup_logging(log_level: str = "INFO", environment: str = "test"):
    """配置结构化日志"""

    # 配置标准库logging
    logging.basicConfig(
        format="%(message)s",
        level=getattr(logging, log_level.upper()),
    )

    # 配置structlog
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
    ]

    if environment == "test":
        # 开发环境使用彩色输出
        processors.append(structlog.dev.ConsoleRenderer())
    else:
        # 生产环境使用JSON格式
        processors.append(structlog.processors.JSONRenderer())

    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
```

### 4.1.2 健康检查端点

```python
# app/api/v1/health.py
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.core.config import get_settings
import asyncio
import time
from typing import Dict, Any

router = APIRouter()

@router.get("/health")
async def health_check():
    """基础健康检查"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": get_settings().app_version
    }

@router.get("/health/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_db)):
    """详细健康检查"""
    checks = {}
    overall_status = "healthy"

    # 数据库连接检查
    try:
        start_time = time.time()
        await db.execute("SELECT 1")
        db_latency = (time.time() - start_time) * 1000
        checks["database"] = {
            "status": "healthy",
            "latency_ms": round(db_latency, 2)
        }
    except Exception as e:
        checks["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        overall_status = "unhealthy"

    # LLM API检查（可选）
    try:
        # 这里可以添加对LLM API的简单检查
        checks["llm_api"] = {
            "status": "healthy",
            "provider": "openai"
        }
    except Exception as e:
        checks["llm_api"] = {
            "status": "degraded",
            "error": str(e)
        }
        if overall_status == "healthy":
            overall_status = "degraded"

    # WebSocket连接统计
    # 这里可以添加WebSocket连接数统计
    checks["websocket"] = {
        "status": "healthy",
        "active_connections": 0  # 从WebSocketManager获取
    }

    response = {
        "status": overall_status,
        "timestamp": time.time(),
        "version": get_settings().app_version,
        "checks": checks
    }

    if overall_status == "unhealthy":
        raise HTTPException(status_code=503, detail=response)

    return response
```

## 4.2 备份与恢复

- **数据库备份**: 提供 `backup_database.sh` 脚本，用于定期执行 `pg_dump` 并清理旧备份。

  ```bash
  #!/bin/bash
  # scripts/backup_database.sh

  set -e

  # 配置
  BACKUP_DIR="/backups"
  DATE=$(date +"%Y%m%d_%H%M%S")
  BACKUP_FILE="crypto_trader_backup_${DATE}.sql"
  RETENTION_DAYS=7

  # 创建备份目录
  mkdir -p $BACKUP_DIR

  # 执行备份
  echo "Starting database backup..."
  pg_dump $DATABASE_URL > "$BACKUP_DIR/$BACKUP_FILE"

  # 压缩备份文件
  gzip "$BACKUP_DIR/$BACKUP_FILE"

  # 清理旧备份
  find $BACKUP_DIR -name "crypto_trader_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete

  echo "Backup completed: $BACKUP_FILE.gz"

  # 可选：上传到云存储
  if [ ! -z "$AWS_S3_BUCKET" ]; then
      aws s3 cp "$BACKUP_DIR/$BACKUP_FILE.gz" "s3://$AWS_S3_BUCKET/backups/"
      echo "Backup uploaded to S3"
  fi
  ```

- **数据库恢复**: 提供 `restore_database.sh` 脚本，用于从备份文件中恢复数据库。

  ```bash
  #!/bin/bash
  # scripts/restore_database.sh

  set -e

  if [ -z "$1" ]; then
      echo "Usage: $0 <backup_file>"
      exit 1
  fi

  BACKUP_FILE=$1

  # 检查备份文件是否存在
  if [ ! -f "$BACKUP_FILE" ]; then
      echo "Backup file not found: $BACKUP_FILE"
      exit 1
  fi

  # 确认操作
  read -p "This will overwrite the current database. Are you sure? (y/N) " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
      echo "Operation cancelled"
      exit 1
  fi

  # 停止应用服务
  echo "Stopping application services..."
  docker-compose stop backend

  # 解压备份文件（如果需要）
  if [[ $BACKUP_FILE == *.gz ]]; then
      gunzip -c "$BACKUP_FILE" | psql $DATABASE_URL
  else
      psql $DATABASE_URL < "$BACKUP_FILE"
  fi

  # 重启应用服务
  echo "Starting application services..."
  docker-compose start backend

  echo "Database restore completed"
  ```

---

# 5. 配置验证脚本

在 CI/CD 流程或容器启动脚本中运行此脚本，可以有效防止因配置错误导致的生产事故。

```python
# scripts/validate_config.py
#!/usr/bin/env python3
"""
配置验证脚本
用于部署前验证环境配置的完整性和正确性
"""

import os
import sys
from typing import List, Tuple
import re

def validate_required_vars() -> List[str]:
    """验证必需的环境变量"""
    required_vars = [
        'APP_SECRET_KEY',
        'JWT_SECRET_KEY',
        'DATABASE_URL',
        'OPENAI_API_KEY'
    ]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    return missing_vars

def validate_secret_strength() -> List[str]:
    """验证密钥强度"""
    issues = []

    # 检查APP_SECRET_KEY
    app_secret = os.getenv('APP_SECRET_KEY', '')
    if len(app_secret) < 32:
        issues.append('APP_SECRET_KEY must be at least 32 characters long')

    if app_secret.startswith('development_') and os.getenv('ENVIRONMENT') == 'production':
        issues.append('Development secret key cannot be used in production')

    # 检查JWT_SECRET_KEY
    jwt_secret = os.getenv('JWT_SECRET_KEY', '')
    if len(jwt_secret) < 32:
        issues.append('JWT_SECRET_KEY must be at least 32 characters long')

    return issues

def validate_database_config() -> List[str]:
    """验证数据库配置"""
    issues = []

    db_url = os.getenv('DATABASE_URL', '')
    if not db_url:
        issues.append('DATABASE_URL is required')
        return issues

    # 检查数据库URL格式
    if not re.match(r'^postgresql\+asyncpg://.+', db_url):
        issues.append('DATABASE_URL must use postgresql+asyncpg driver')

    # 生产环境不能使用内存数据库
    if ':memory:' in db_url and os.getenv('ENVIRONMENT') == 'production':
        issues.append('In-memory database cannot be used in production')

    return issues

def validate_api_config() -> List[str]:
    """验证API配置"""
    issues = []

    # 检查端口号
    try:
        port = int(os.getenv('API_PORT', '8000'))
        if not (1 <= port <= 65535):
            issues.append('API_PORT must be between 1 and 65535')
    except ValueError:
        issues.append('API_PORT must be a valid integer')

    # 检查CORS配置
    cors_origins = os.getenv('CORS_ORIGINS', '')
    if not cors_origins and os.getenv('ENVIRONMENT') == 'production':
        issues.append('CORS_ORIGINS should be configured for production')

    return issues

def main():
    """主验证函数"""
    print("🔍 Validating configuration...")

    all_issues = []

    # 验证必需变量
    missing_vars = validate_required_vars()
    if missing_vars:
        all_issues.extend([f"Missing required variable: {var}" for var in missing_vars])

    # 验证密钥强度
    all_issues.extend(validate_secret_strength())

    # 验证数据库配置
    all_issues.extend(validate_database_config())

    # 验证API配置
    all_issues.extend(validate_api_config())

    if all_issues:
        print("❌ Configuration validation failed:")
        for issue in all_issues:
            print(f"  - {issue}")
        sys.exit(1)
    else:
        print("✅ Configuration validation passed!")
        sys.exit(0)

if __name__ == '__main__':
    main()
```

---

# 6. 技术栈

|                  |                        |                                                                                        |
| ---------------- | ---------------------- | -------------------------------------------------------------------------------------- |
| **领域**         | **技术/框架**          | **备注**                                                                               |
| **依赖管理**     | Anaconda/Miniconda     | **推荐**：统一的 Python 环境和包管理，支持跨平台，解决依赖冲突，提供优化的科学计算库。 |
| **备选依赖管理** | pip + requirements.txt | 传统方式，适合简单部署场景。                                                           |
| **容器化**       | Docker                 | 容器化部署，简化流程。                                                                 |
| **编排工具**     | Docker Compose         | 多容器应用编排。                                                                       |
| **后端测试**     | pytest                 | Python 测试框架。                                                                      |
| **前端测试**     | Vitest                 | 基于 Vite 的测试框架。                                                                 |
| **代码质量**     | Black, isort, flake8   | Python 代码格式化和静态分析工具。                                                      |

---

## 总结

本文档涵盖了AI Crypto Trading Agent项目的完整测试部署运维体系：

### 混合开发环境特点
- **稳定的数据库服务**: PostgreSQL在Docker中运行，避免数据丢失
- **高效的本地开发**: 后端和前端在本地运行，避免容器重启问题
- **原生热重载**: 享受更快的文件监听和自动重启体验
- **灵活的调试**: 更好的IDE集成和错误追踪能力

### 生产环境特点
- **多阶段构建**: 优化镜像大小和安全性
- **配置管理**: 分层配置策略，支持多环境部署
- **可观测性**: 结构化日志和健康检查端点
- **备份恢复**: 自动化数据库备份和恢复流程

### 最佳实践
1. **开发阶段**: 使用混合环境进行日常开发，享受稳定高效的开发体验
2. **测试阶段**: 运行完整的自动化测试套件验证功能
3. **部署阶段**: 使用配置验证脚本确保环境配置正确
4. **运维阶段**: 监控服务健康状态，定期执行备份

### 环境选择指南
- **日常开发**: 推荐使用混合环境 (PostgreSQL Docker + 本地服务)
- **CI/CD**: 可以使用完整Docker环境确保一致性
- **生产部署**: 使用优化的Docker Compose配置

## 7. 测试性能优化要点

### 主要性能问题
- Vitest单元测试: 4.96秒 (目标<3秒)
- 测试环境启动: 7.63秒 (目标<3秒)
- Vuetify重复警告影响输出

### 简单优化措施
1. **修复Vuetify重复警告** - 使用单例模式
2. **优化WebSocket Mock** - 减少连接延迟
3. **并行执行** - 启用Vitest threads模式

### 下一步计划
1. **CI/CD集成**: 在GitHub Actions中集成Docker Compose测试环境 ✅
2. **监控增强**: 添加Prometheus和Grafana监控服务
3. **安全加固**: 实施容器安全最佳实践
4. **性能优化**: 优化容器启动时间和资源使用 ✅
5. **测试智能化**: 实施测试选择和并行化策略 🆕
6. **质量自动化**: 建立自动化质量门禁和报告 🆕

通过本文档的指导，开发团队可以高效地进行项目开发、测试和部署，确保系统的稳定性和可维护性。

---

**文档更新**: 2025-07-24 - 添加测试性能监控和CI/CD优化策略
