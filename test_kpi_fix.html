<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KPI格式化函数修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .test-case {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>KPI格式化函数修复验证</h1>
        <p>这个工具测试修复后的格式化函数是否能正确处理undefined、null和异常值。</p>
        
        <div id="testStatus" class="status info">点击按钮开始测试</div>
        <button onclick="runAllTests()">运行所有测试</button>
        
        <div id="testResults" style="display: none;">
            <h3>测试结果</h3>
            <div id="testDetails"></div>
        </div>
    </div>

    <script>
        // 模拟修复后的格式化函数
        const monitoringUtils = {
            formatDuration(seconds) {
                if (seconds === undefined || seconds === null || isNaN(seconds)) {
                    return '0秒'
                }
                
                const numSeconds = Number(seconds)
                if (numSeconds < 60) {
                    return `${numSeconds.toFixed(1)}秒`
                } else if (numSeconds < 3600) {
                    return `${(numSeconds / 60).toFixed(1)}分钟`
                } else {
                    return `${(numSeconds / 3600).toFixed(1)}小时`
                }
            },

            formatPercentage(value, decimals = 1) {
                if (value === undefined || value === null || isNaN(value)) {
                    return '0%'
                }
                
                const numValue = Number(value)
                return `${numValue.toFixed(decimals)}%`
            },

            formatNumber(value) {
                if (value === undefined || value === null || isNaN(value)) {
                    return '0'
                }
                
                const numValue = Number(value)
                if (numValue >= 1000000) {
                    return `${(numValue / 1000000).toFixed(1)}M`
                } else if (numValue >= 1000) {
                    return `${(numValue / 1000).toFixed(1)}K`
                } else {
                    return numValue.toString()
                }
            }
        };

        // 模拟KpiCard的formattedValue函数
        function formatValue(value, format) {
            if (value === undefined || value === null) {
                return '0'
            }
            
            switch (format) {
                case 'percentage':
                    return monitoringUtils.formatPercentage(value)
                case 'duration':
                    return monitoringUtils.formatDuration(value)
                case 'number':
                    return monitoringUtils.formatNumber(value)
                default:
                    return String(value)
            }
        }

        // 测试用例
        const testCases = [
            // formatNumber 测试
            { func: 'formatNumber', input: undefined, expected: '0', description: 'undefined值' },
            { func: 'formatNumber', input: null, expected: '0', description: 'null值' },
            { func: 'formatNumber', input: NaN, expected: '0', description: 'NaN值' },
            { func: 'formatNumber', input: 0, expected: '0', description: '零值' },
            { func: 'formatNumber', input: 42, expected: '42', description: '普通数字' },
            { func: 'formatNumber', input: 1500, expected: '1.5K', description: '千位数' },
            { func: 'formatNumber', input: 2500000, expected: '2.5M', description: '百万位数' },
            
            // formatPercentage 测试
            { func: 'formatPercentage', input: undefined, expected: '0%', description: 'undefined值' },
            { func: 'formatPercentage', input: null, expected: '0%', description: 'null值' },
            { func: 'formatPercentage', input: NaN, expected: '0%', description: 'NaN值' },
            { func: 'formatPercentage', input: 0, expected: '0.0%', description: '零值' },
            { func: 'formatPercentage', input: 75.5, expected: '75.5%', description: '普通百分比' },
            
            // formatDuration 测试
            { func: 'formatDuration', input: undefined, expected: '0秒', description: 'undefined值' },
            { func: 'formatDuration', input: null, expected: '0秒', description: 'null值' },
            { func: 'formatDuration', input: NaN, expected: '0秒', description: 'NaN值' },
            { func: 'formatDuration', input: 0, expected: '0.0秒', description: '零值' },
            { func: 'formatDuration', input: 30, expected: '30.0秒', description: '秒数' },
            { func: 'formatDuration', input: 120, expected: '2.0分钟', description: '分钟数' },
            { func: 'formatDuration', input: 7200, expected: '2.0小时', description: '小时数' },
            
            // formatValue 测试（KpiCard组件）
            { func: 'formatValue', input: [undefined, 'number'], expected: '0', description: 'KpiCard undefined值' },
            { func: 'formatValue', input: [null, 'percentage'], expected: '0', description: 'KpiCard null值' },
            { func: 'formatValue', input: [75.5, 'percentage'], expected: '75.5%', description: 'KpiCard 百分比' },
            { func: 'formatValue', input: [120, 'duration'], expected: '2.0分钟', description: 'KpiCard 时长' },
            { func: 'formatValue', input: [1500, 'number'], expected: '1.5K', description: 'KpiCard 数字' }
        ];

        function runAllTests() {
            const status = document.getElementById('testStatus');
            const results = document.getElementById('testResults');
            const details = document.getElementById('testDetails');
            
            status.className = 'status info';
            status.innerHTML = '🔄 正在运行测试...';
            
            let passedTests = 0;
            let failedTests = 0;
            let testOutput = '';
            
            testCases.forEach((testCase, index) => {
                try {
                    let result;
                    
                    if (testCase.func === 'formatValue') {
                        result = formatValue(testCase.input[0], testCase.input[1]);
                    } else {
                        result = monitoringUtils[testCase.func](testCase.input);
                    }
                    
                    const passed = result === testCase.expected;
                    
                    if (passed) {
                        passedTests++;
                    } else {
                        failedTests++;
                    }
                    
                    testOutput += `
                        <div class="test-case">
                            <strong>测试 ${index + 1}: ${testCase.func} - ${testCase.description}</strong><br>
                            输入: ${JSON.stringify(testCase.input)}<br>
                            期望: ${testCase.expected}<br>
                            实际: ${result}<br>
                            结果: ${passed ? '✅ 通过' : '❌ 失败'}
                        </div>
                    `;
                    
                } catch (error) {
                    failedTests++;
                    testOutput += `
                        <div class="test-case">
                            <strong>测试 ${index + 1}: ${testCase.func} - ${testCase.description}</strong><br>
                            输入: ${JSON.stringify(testCase.input)}<br>
                            错误: ${error.message}<br>
                            结果: ❌ 异常
                        </div>
                    `;
                }
            });
            
            // 显示总体结果
            if (failedTests === 0) {
                status.className = 'status success';
                status.innerHTML = `✅ 所有测试通过！(${passedTests}/${passedTests + failedTests})`;
            } else {
                status.className = 'status error';
                status.innerHTML = `❌ 部分测试失败 (${passedTests}/${passedTests + failedTests})`;
            }
            
            details.innerHTML = testOutput;
            results.style.display = 'block';
        }
    </script>
</body>
</html>
