# 🏗️ 项目测试规范

**版本**: 2.3 | **更新**: 2025-07-27 | **项目**: AI Crypto Trading Agent

## 📚 快速导航

### 🚀 立即开始 (AI助手优先)
- [🔧 测试执行指南](#-测试执行指南) - 原生命令 + 脚本使用
- [🎯 核心原则](#-核心原则) - 测试基本原则和强制流程
- [🔧 故障排除指南](#-故障排除指南) - 常见问题快速解决

### 📖 编写指南
- [📝 测试编写规范](#-测试编写规范) - 思考清单 + 命名规范
- [📊 质量标准](#-质量标准) - 覆盖率和性能要求
- [🎯 AI 测试生成指导](#-ai-测试生成指导) - AI辅助测试编写

### 🔗 相关文档
- [🔄 维护规范](#-维护规范) - 代码变更时的测试更新
- [📚 相关文档](#-相关文档) - 测试文档体系

## �️ 环境要求（强制）

**必须使用conda环境：**
- **环境名称**：`crypto-trader`（统一环境名称）
- **激活方式**：`conda activate crypto-trader` 或 `conda run -n crypto-trader <command>`
- **验证命令**：`python scripts/env.py test --validate-only`

## �🎯 核心原则

### 真实数据原则 (强制)
- **API/E2E 测试**: 必须使用真实后端数据，严禁 mock
- **数据一致性**: 确保前后端实时同步
- **集成验证**: 验证真实 API 调用和数据库操作

### 分层测试策略
```
E2E (Playwright) → API (Playwright) → 组件 (Vitest) → 单元 (pytest) → 数据库 (pytest)
```

### AI助手执行原则 (强制)
**任何代码修改后，AI助手必须按以下优先级执行：**

1. **快速诊断** - 使用原生命令定位具体问题
2. **问题修复** - 针对性修复代码
3. **局部验证** - 使用原生命令验证修复效果
4. **完整验证** - 执行脚本进行全面测试

### 强制分步测试流程
**完整验证阶段必须按顺序执行，失败立即修复：**
1. `python scripts/test.py --all` (查看清单)
2. 按步骤逐个执行测试命令
3. `python scripts/test.py --execute-all` (最终验证)

## 🔧 测试执行指南

### 🚀 原生命令 (AI助手优先使用)

#### 后端测试
```bash
# 在 backend/ 目录下执行

# 快速诊断特定问题
python -m pytest tests/unit/test_auth.py::TestAuthManager::test_authenticate_user_success -v

# 运行特定测试文件
python -m pytest tests/unit/test_auth.py -v

# 运行测试类型
python -m pytest tests/unit/ -v --tb=short --strict-markers --disable-warnings
python -m pytest tests/integration/ -v --tb=short --strict-markers --disable-warnings

# 快速失败模式 (遇到第一个错误就停止)
python -m pytest tests/unit/ -x -v

# 生成覆盖率报告
python -m pytest tests/unit/ --cov=app --cov-report=html --cov-report=term

# 性能分析 (显示最慢的10个测试)
python -m pytest tests/unit/ --durations=10
```

#### 前端单元/组件测试
```bash
# 在 frontend/ 目录下执行

# 快速诊断特定组件
npx vitest run tests/components/SignalsView.test.js

# 运行测试类型
npx vitest run tests/unit/                   # 单元测试
npx vitest run tests/components/             # 组件测试

# 监听模式 (开发时使用)
npx vitest

# 生成覆盖率报告
npx vitest --coverage

# UI界面模式
npx vitest --ui

# 快速失败模式
npx vitest run tests/unit/ --reporter=verbose --bail=1
```

#### 前端E2E/API测试 (并行测试优先)
```bash
# 在 frontend/ 目录下执行

# 🚀 并行测试 (推荐默认方式)
npm run test:parallel:fast                    # 快速并行测试
npm run test:parallel:standard                # 标准并行测试
npm run test:parallel:full                    # 完整并行测试

# 🔧 并行测试调试
npm run test:parallel:debug                   # 调试模式
npm run test:parallel:headed                  # 有头模式
node run-parallel-tests.js --help             # 查看所有选项

# 📊 并行测试报告
npm run test:e2e:report                       # 查看HTML报告

# ⚡ 原生Playwright命令 (仅用于特定调试)
npx playwright test --config playwright-parallel.config.js  # 使用并行配置
npx playwright test tests/e2e/auth.test.js --debug          # 调试特定测试
npx playwright test --project=api-fast                      # 只运行API测试
npx playwright test --project=core-features                 # 只运行核心功能测试

# 🎯 测试策略选择
# fast: API测试 + 核心功能 (适合开发时快速验证)
# standard: 包含业务流程和错误处理 (适合提交前验证)
# full: 包含性能和系统测试 (适合发布前验证)
```

#### 代码质量检查
```bash
# 代码格式检查
/opt/anaconda3/envs/crypto-trader/bin/python -m black --check app/

# 自动格式化
/opt/anaconda3/envs/crypto-trader/bin/python -m black app/

# 类型检查
/opt/anaconda3/envs/crypto-trader/bin/python -m mypy app/ --ignore-missing-imports --explicit-package-bases

# 前端代码检查
cd frontend && npm run lint
```

### 🎯 使用场景指导

#### 场景1：修复后端API问题
```bash
# 1. 快速验证特定API测试
cd backend && python -m pytest tests/unit/test_auth.py::TestAuthManager::test_authenticate_user_success -v

# 2. 修复代码后重新验证
cd backend && python -m pytest tests/unit/test_auth.py -v

# 3. 确认无问题后执行完整验证
python scripts/test.py --pytest unit
```

#### 场景2：修复前端组件问题
```bash
# 1. 快速验证特定组件测试
cd frontend && npx vitest run tests/components/SignalsView.test.js

# 2. 修复代码后重新验证
cd frontend && npx vitest run tests/components/

# 3. 确认无问题后执行完整验证
python scripts/test.py --vitest components
```

#### 场景3：修复E2E流程问题
```bash
# 1. 快速验证特定E2E测试 (使用并行配置)
cd frontend && npx playwright test tests/e2e/auth.test.js --config playwright-parallel.config.js --grep "用户登录"

# 2. 调试模式定位问题
cd frontend && npm run test:parallel:debug

# 3. 修复后重新验证 (快速并行测试)
cd frontend && npm run test:parallel:fast

# 4. 确认无问题后执行完整验证
cd frontend && npm run test:parallel:standard
```

### 🚀 脚本方式 (完整验证推荐)

#### 环境准备

##### 服务启动命令
```bash
# 检查当前服务状态
python scripts/test.py --status

# 启动测试环境 (混合开发环境)
docker-compose -f docker-compose.test.yml up -d postgres-test

# 启动后端服务 (新终端)
cd backend && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 启动前端开发服务 (新终端)
cd frontend && npm run dev

# 验证环境连通性
python scripts/env.py test --verify-hybrid
```

##### 服务停止命令
```bash
# 停止本地服务 (Ctrl+C 在对应终端)

# 停止数据库服务
docker-compose -f docker-compose.test.yml down

# 强制清理所有容器和网络
docker-compose down --volumes --remove-orphans
```

#### 分步测试流程
```bash
# 第一步：查看测试命令清单
python scripts/test.py --all

# 第二步：按顺序逐个执行测试命令
python scripts/test/backend/run_backend_tests.py unit
python scripts/test/backend/run_backend_tests.py integration
conda run -n crypto-trader python -m black --check app/
conda run -n crypto-trader python -m mypy app/ --ignore-missing-imports --explicit-package-bases
node scripts/test/frontend/run_unit_tests.js unit
node scripts/test/frontend/run_unit_tests.js components
python scripts/test/frontend/run_api_tests.py all
python scripts/test/frontend/run_e2e_tests.py all

# 第三步：最终完整验证
python scripts/test.py --execute-all
```

#### 快速执行命令
```bash
# 后端测试快速执行
python scripts/test.py --pytest unit           # 后端单元测试
python scripts/test.py --pytest integration   # 后端集成测试

# 前端测试快速执行 (并行测试优先)
cd frontend && npm run test:parallel:fast      # 并行快速测试 (推荐)
cd frontend && npm run test:parallel:standard  # 并行标准测试
python scripts/test.py --vitest unit           # 前端单元测试
python scripts/test.py --vitest components     # 前端组件测试

# 传统方式 (仅用于特殊调试)
python scripts/test.py --playwright api        # 前端API测试
python scripts/test.py --playwright e2e        # 前端E2E测试
```

### ⚠️ 环境依赖说明
- **后端测试**：需要PostgreSQL数据库运行（端口5432）
- **前端API测试**：需要后端服务运行（端口8000）
- **前端E2E测试**：需要前后端服务都运行（端口8000+5173）
- **前端单元/组件测试**：无外部依赖

### 🔄 执行顺序建议
1. **优先执行无依赖测试**：前端单元/组件测试
2. **其次执行后端测试**：需要数据库
3. **最后执行集成测试**：需要完整服务栈

## 📝 测试编写规范

### 测试编写思考清单 (编写前必答)

#### 核心问题
1. **目标明确性**
   - [ ] 验证什么具体功能？
   - [ ] 成功标准是什么？
   - [ ] 失败时能明确定位问题？
   - [ ] 是否与现有测试重复？

2. **层级选择**
   - [ ] 单元测试：独立函数/类逻辑
   - [ ] 组件测试：UI 组件行为
   - [ ] API 测试：接口功能和数据
   - [ ] E2E 测试：完整用户流程

3. **数据策略**
   - [ ] 使用真实数据还是 mock？
     * **单元测试**: 可使用mock隔离外部依赖
     * **组件测试**: 可使用mock隔离API调用
     * **API集成测试**: 必须使用真实后端数据
     * **E2E测试**: 必须使用完整真实环境
   - [ ] 如何确保数据隔离？
     * 使用唯一标识符（时间戳、UUID）
     * 每个测试使用独立的测试用户
     * 避免测试间的数据竞争
   - [ ] 数据生命周期管理？
     * 测试开始前创建必要数据
     * 测试过程中维护数据状态
     * 测试结束后自动清理数据
   - [ ] 测试后如何清理？
     * API测试：删除创建的资源
     * E2E测试：清理用户和相关数据
     * 数据库测试：回滚事务或清理表

4. **依赖分析**
   - [ ] 依赖哪些外部服务？
   - [ ] 服务不可用如何处理？
   - [ ] 执行顺序是否重要？
   - [ ] 并发执行是否冲突？

5. **维护性**
   - [ ] 代码易于理解维护？
   - [ ] 业务变更时易于更新？
   - [ ] 文档注释是否充分？
   - [ ] 错误信息是否清晰？

### 命名规范

#### 文件命名
```bash
# 后端测试 (一对一原则)
test_[模块名].py              # 单元测试，一个类对应一个测试文件
test_[模块名]_integration.py  # 集成测试

# 前端测试
[组件名].test.js             # 组件测试 (PascalCase)
[功能名].api.test.js         # API 测试 (camelCase)
[流程名].test.js             # E2E 测试
```

#### 测试函数命名
```python
# Python: test_{功能描述}_{预期结果}
def test_find_orders_by_criteria_success():
    """测试按条件查找订单成功"""
    pass

def test_authenticate_user_invalid_credentials():
    """测试用户认证失败 - 无效凭据"""
    pass
```

```javascript
// JavaScript: 描述性中文命名
test('应该在用户输入有效凭据时成功登录', async () => {})
test('应该在密码错误时显示错误消息', async () => {})
```

#### 测试类命名
```python
# 格式：Test{功能模块名}
class TestAgentTools:        # 对应 agent_tools.py
class TestAuthManager:       # 对应 auth.py
class TestUserModel:         # 对应 models.py 中的 User 类
```

### 语言规范
**强制规范**：测试代码的注释和输出信息必须以中文为主

**适用范围**：
- **测试函数文档字符串**：必须使用中文描述测试目的和预期结果
- **测试内部注释**：解释测试逻辑的注释优先使用中文
- **断言错误信息**：自定义错误消息必须使用中文
- **测试数据标识**：测试数据的描述性字段使用中文
- **日志输出**：测试过程中的日志信息使用中文

**例外情况**：
- **技术术语**：保留英文的专业技术术语（如 API、HTTP、JSON 等）
- **代码标识符**：变量名、函数名等代码标识符仍使用英文
- **第三方库**：第三方库的错误信息保持原文
- **国际化测试**：专门测试多语言功能时可使用对应语言

### AAA 模式 (标准结构)
```javascript
test('应该创建新订单并返回订单ID', async () => {
  // Arrange - 准备
  const testUser = await TestDataFactory.createUser()
  const orderData = { symbol: 'BTC/USDT', side: 'buy', amount: 0.001 }

  // Act - 执行
  const response = await api.createOrder(orderData, testUser.token)

  // Assert - 验证
  expect(response.status).toBe(201)
  expect(response.data.orderId).toBeDefined()

  // Cleanup - 清理
  await TestDataFactory.cleanup(response.data.orderId)
})
```

### 测试用例设计原则

#### UI系统测试设计
```javascript
// 验证UI组件加载
const uiComponents = [
  '[data-testid="main-navigation"]',
  '.v-app-bar',
  '.v-navigation-drawer'
]

// 验证响应式设计
await page.setViewportSize({ width: 375, height: 667 }) // 移动端
await page.setViewportSize({ width: 1920, height: 1080 }) // 桌面端
```

#### 用户引导流程测试
```javascript
test('新用户引导流程', async ({ page }) => {
  // 模拟首次访问
  await page.evaluate(() => {
    localStorage.clear()
    sessionStorage.clear()
  })

  // 验证引导步骤
  const guidanceSteps = [
    'text=欢迎使用',
    'text=功能介绍',
    'text=开始使用'
  ]

  for (const step of guidanceSteps) {
    await expect(page.locator(step)).toBeVisible()
    await page.click('text=下一步')
  }
})
```

#### 错误边界测试
```javascript
test('错误边界处理', async ({ page }) => {
  // 模拟JavaScript错误
  await page.addInitScript(() => {
    window.addEventListener('error', (e) => {
      console.log('捕获到错误:', e.error)
    })
  })

  // 触发错误并验证恢复
  await page.evaluate(() => {
    throw new Error('模拟错误')
  })

  // 验证错误处理UI
  await expect(page.locator('text=出现错误')).toBeVisible()
  await expect(page.locator('text=重试')).toBeVisible()
})
```

### 错误处理
```javascript
// 异常场景
test('应该在无效token时返回401错误', async () => {
  await expect(api.getUserProfile('invalid-token'))
    .rejects.toMatchObject({
      status: 401,
      message: expect.stringContaining('无效的认证token')
    })
})

// 重试机制
test('应该在网络超时时进行重试', async () => {
  const mockApi = vi.fn()
    .mockRejectedValueOnce(new Error('Network timeout'))
    .mockResolvedValueOnce({ data: 'success' })

  const result = await retryableApiCall(mockApi)
  expect(mockApi).toHaveBeenCalledTimes(2)
})
```

## 📊 质量标准

### 覆盖率要求
- **API 端点**: 100% (所有 REST API)
- **核心业务**: 90% (交易和风险管理)
- **UI 组件**: 70% (主要界面组件)
- **E2E 流程**: 80% (主要用户旅程)

### 性能要求
- **单元测试**: < 5 分钟
- **集成测试**: < 10 分钟
- **E2E 测试**: < 15 分钟
- **单个测试**: < 30 秒

### 稳定性要求
- **通过率**: ≥ 95%
- **重复性**: 连续 3 次一致
- **并发性**: 支持并行执行

## 🎯 AI 测试生成指导

### AI助手测试执行指导原则

#### 原生命令优先原则 (强制)
**AI助手在修复具体问题时，必须遵循"原生命令优先"的执行策略：**

1. **快速诊断阶段**：优先使用原生测试命令定位问题
   ```bash
   # 示例：修复认证问题时
   cd backend && python -m pytest tests/unit/test_auth.py::TestAuthManager::test_authenticate_user_success -v
   ```

2. **问题修复阶段**：针对性修复代码后，使用原生命令验证
   ```bash
   # 示例：修复后立即验证
   cd backend && python -m pytest tests/unit/test_auth.py -v
   ```

3. **局部验证阶段**：确认修复效果后，扩大测试范围
   ```bash
   # 示例：验证相关功能
   cd backend && python -m pytest tests/unit/ -k "auth" -v
   ```

4. **完整验证阶段**：最后执行脚本进行全面测试
   ```bash
   # 示例：完整验证
   python scripts/test.py --pytest unit
   ```

#### 测试命令选择指导
**根据问题类型选择合适的原生命令：**

- **后端API问题**：`cd backend && python -m pytest tests/unit/test_[模块].py -v`
- **前端组件问题**：`cd frontend && npx vitest run tests/components/[组件].test.js`
- **E2E流程问题**：`cd frontend && npm run test:parallel:debug` (并行调试模式)
- **API集成问题**：`cd frontend && npx playwright test tests/api-unified/[模块].api.test.js --config playwright-parallel.config.js`
- **快速验证修复**：`cd frontend && npm run test:parallel:fast`

#### 高效调试策略
```bash
# 快速失败模式（遇到第一个错误就停止）
cd backend && python -m pytest tests/unit/ -x -v

# 详细输出模式（显示完整错误信息）
cd frontend && npx vitest run tests/components/ --reporter=verbose

# 调试模式（交互式调试）
cd frontend && npx playwright test tests/e2e/auth.test.js --debug

# 特定测试模式（只运行匹配的测试）
cd frontend && npx playwright test --grep "用户登录"
```

### 生成指导框架
1. 使用 **[测试清单](./项目测试清单.md)** 的格式标准和优先级
2. 遵循本文档的编写规范和思考清单
3. 参考 **[测试经验](./项目测试经验.md)** 的最佳实践
4. 使用 **[测试配置](./项目测试配置.md)** 的环境设置
5. 借鉴已实现测试的模式和标准
6. **优先使用原生命令进行问题诊断和局部验证**

### 质量保证
- 覆盖核心业务逻辑
- 实现分层测试策略
- 包含错误处理和边界条件
- 遵循真实数据原则
- **使用原生命令快速验证修复效果**

### 强制要求
- **必须先执行原生命令诊断，再执行脚本验证**
- 任何失败立即修复
- 同步更新相关组件
- 遵循命名规范
- **原生命令测试通过后，才能执行分步测试流程**

## 🔧 故障排除指南

### 常见问题快速解决

#### 数据库连接问题
```bash
# 问题：PostgreSQL连接失败
# 解决方案：
docker-compose ps postgres                      # 检查容器状态
docker-compose logs postgres                    # 查看数据库日志
docker-compose restart postgres                 # 重启数据库
docker-compose -f docker-compose.test.yml up -d postgres  # 使用测试配置启动

# 验证连接
python -c "import psycopg2; psycopg2.connect('postgresql://postgres:password@localhost:5432/crypto_trader')"
```

#### 后端服务问题
```bash
# 问题：后端服务无法启动或API测试失败
# 解决方案：
docker-compose logs backend                     # 查看后端日志
docker-compose restart backend                  # 重启后端服务
curl http://localhost:8000/health               # 测试健康检查端点

# 手动启动后端进行调试
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端服务问题
```bash
# 问题：前端服务启动失败或E2E测试失败
# 解决方案：
cd frontend
npm install                                     # 重新安装依赖
npm run dev                                     # 启动开发服务器
curl http://localhost:5173                     # 测试前端服务

# 清理缓存
rm -rf node_modules package-lock.json
npm install
```

#### 测试环境问题
```bash
# 问题：测试数据污染或测试失败
# 解决方案：
python scripts/db/run_sqls.py cleanup_data.sql  # 清理测试数据
python scripts/db/run_sqls.py mock_data.sql     # 重新生成测试数据

# 完全重置测试环境
docker-compose -f docker-compose.test.yml down --volumes
docker-compose -f docker-compose.test.yml up -d
```

#### 权限和路径问题
```bash
# 问题：Python路径或权限错误
# 解决方案：
which python                                    # 检查Python路径
conda run -n crypto-trader python --version    # 检查conda环境
chmod +x scripts/test/*.py                      # 修复执行权限
export PYTHONPATH="${PYTHONPATH}:$(pwd)"       # 设置Python路径
```

### 紧急恢复命令
```bash
# 完全重置开发环境
docker-compose down --volumes --remove-orphans
docker system prune -f
docker-compose -f docker-compose.test.yml up -d

# 重新安装前端依赖
cd frontend && rm -rf node_modules package-lock.json && npm install

# 验证环境
python scripts/env.py test --verify-hybrid
python scripts/test.py --status
```

### 测试结果查看

#### 测试报告访问
```bash
# 并行测试报告 (推荐)
npm run test:e2e:report                                    # 自动打开并行测试HTML报告
open ../temp/frontend/playwright/parallel-report/index.html  # 手动打开并行测试报告

# 其他测试报告
open ../temp/backend/coverage/html/index.html             # 后端测试覆盖率报告
open frontend/coverage/index.html                         # 前端单元测试覆盖率报告

# 并行测试结果文件
cat ../temp/frontend/playwright/parallel-results.json    # JSON格式结果
cat ../temp/frontend/playwright/parallel-junit.xml       # JUnit格式结果
```

#### 日志文件查看
```bash
# 查看最新的测试日志
tail -f temp/test_logs/latest.log

# 查看特定测试的日志
ls temp/test_logs/                              # 列出所有日志文件
cat temp/test_logs/backend_unit_test.log        # 后端单元测试日志
cat temp/test_logs/frontend_e2e_test.log        # 前端E2E测试日志
```

#### 快速问题定位
```bash
# 查看失败的测试用例
grep -r "FAILED" temp/test_logs/

# 查看错误信息
grep -r "ERROR\|Exception" temp/test_logs/

# 查看数据库日志
docker-compose -f docker-compose.test.yml logs --tail=50 postgres-test

# 查看本地服务日志 (在对应终端查看输出)
```

### 获取帮助
- **测试失败**: 查看 `temp/test_logs/` 目录下的详细日志
- **环境问题**: 运行 `python scripts/env.py test --verify-hybrid` 进行诊断
- **服务状态**: 运行 `python scripts/test.py --status` 检查所有服务
- **文档参考**: 查看 `docs/test/` 目录下的其他测试文档

## 🔄 维护规范

### 代码变更时的测试更新
**修改代码时必须同步更新相关测试，详细的同步更新清单请参考：[项目规范](../0. 项目规范.md)**

**核心要求**：
- **新功能**: 必须包含对应的测试用例
- **Bug 修复**: 必须添加回归测试
- **重构**: 必须确保测试仍然有效
- **强制验证**: 按分步测试流程执行

### 定期维护
- **每周**: 检查失败测试，更新数据
- **每月**: 审查覆盖率，清理过时测试
- **每季度**: 评估架构，优化性能
- **变更时**: 同步更新相关文档

## 🚀 并行测试最佳实践

### 测试策略选择
**根据开发阶段选择合适的并行测试策略：**

- **开发阶段** (`fast`): 包含API测试和核心功能
- **提交前** (`standard`): 包含业务流程和错误处理
- **发布前** (`full`): 包含性能和系统测试

### 数据隔离原则
- **用户隔离**: 每个测试组使用独立的测试用户
- **数据清理**: 测试完成后自动清理临时数据
- **并发安全**: 避免测试间的数据竞争

### 性能优化
- **Worker配置**: 根据系统资源自动调整并行数量
- **测试分组**: 按依赖关系和执行时间合理分组
- **超时设置**: 针对不同测试类型设置合理超时

### 故障处理
- **重试机制**: 网络相关测试自动重试
- **错误隔离**: 单个测试失败不影响其他测试
- **详细日志**: 提供完整的执行日志和错误信息

### 报告分析
- **统一输出**: 所有报告输出到 `../temp/frontend/playwright/` 目录
- **多格式支持**: HTML、JSON、JUnit等格式
- **性能指标**: 包含执行时间和并行效率统计

## 📚 相关文档

### 测试文档体系
- **[测试规范](./项目测试规范.md)** - 本文档，编写和执行规范
- **[测试清单](./项目测试清单.md)** - 覆盖清单和优先级
- **[测试经验](./项目测试经验.md)** - 问题解决和调试技巧
- **[测试配置](./项目测试配置.md)** - 环境配置和参数

### 相关文档
- **[测试设计文档](./测试设计文档.md)** - 分层测试架构和策略
- **[测试清单](./项目测试清单.md)** - 测试覆盖指导和实现状态
- **[测试经验](./项目测试经验.md)** - 快速诊断和详细解决方案
- **[项目规范](../0. 项目规范.md)** - 命名和质量标准

---
**维护**: 活文档，随项目发展更新 | **更新**: 2025-07-27 | **审查**: 2025-08-27
