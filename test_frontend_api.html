<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API调用测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button.success { background-color: #28a745; }
        button.success:hover { background-color: #218838; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端API调用测试工具</h1>
        <p>这个工具模拟前端应用的API调用，帮助诊断监控页面数据显示问题。</p>
        
        <div class="test-section">
            <h3>1. 认证状态</h3>
            <div id="authStatus" class="status info">点击按钮检查认证状态</div>
            <button onclick="checkAuth()">检查认证状态</button>
            <button onclick="doLogin()">执行登录</button>
        </div>
        
        <div class="test-section">
            <h3>2. 监控API测试</h3>
            <div id="apiStatus" class="status info">点击按钮测试API</div>
            <button onclick="testKPIAPI()">测试KPI API</button>
            <button onclick="testFlowStatusAPI()">测试流程状态API</button>
            <button onclick="testRecentSignalsAPI()">测试最近信号API</button>
            <button onclick="testActiveAlertsAPI()">测试活跃告警API</button>
            <button onclick="testAllAPIs()">测试所有API</button>
        </div>
        
        <div class="test-section">
            <h3>3. 前端数据流模拟</h3>
            <div id="dataFlowStatus" class="status info">点击按钮模拟前端数据流</div>
            <button onclick="simulateFrontendDataFlow()">模拟完整数据流</button>
            <button onclick="testCachedAPI()">测试缓存API调用</button>
        </div>
        
        <div id="results" class="container" style="display: none;">
            <h3>测试结果</h3>
            <pre id="resultDetails"></pre>
        </div>
    </div>

    <script>
        let authToken = null;
        
        // 检查认证状态
        async function checkAuth() {
            const authStatus = document.getElementById('authStatus');
            
            try {
                // 检查sessionStorage中的token
                const encryptedToken = sessionStorage.getItem('auth_token');
                
                if (!encryptedToken) {
                    authStatus.className = 'status warning';
                    authStatus.innerHTML = '⚠️ 未找到认证token，需要先登录';
                    return false;
                }
                
                // 解密token
                try {
                    authToken = decodeURIComponent(atob(encryptedToken));
                } catch (e) {
                    authToken = encryptedToken;
                }
                
                // 验证token
                const response = await fetch('http://localhost:8000/api/v1/auth/me', {
                    headers: {
                        'Authorization': 'Bearer ' + authToken,
                        'Accept': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const userData = await response.json();
                    authStatus.className = 'status success';
                    authStatus.innerHTML = '✅ 认证成功，用户: ' + userData.username;
                    return true;
                } else {
                    authStatus.className = 'status error';
                    authStatus.innerHTML = '❌ Token无效或已过期';
                    return false;
                }
                
            } catch (error) {
                authStatus.className = 'status error';
                authStatus.innerHTML = '❌ 认证检查失败: ' + error.message;
                return false;
            }
        }
        
        // 执行登录
        async function doLogin() {
            const authStatus = document.getElementById('authStatus');
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'username=demo&password=password123'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.access_token;
                    
                    // 模拟前端存储token的方式
                    const encrypted = btoa(encodeURIComponent(authToken));
                    sessionStorage.setItem('auth_token', encrypted);
                    
                    authStatus.className = 'status success';
                    authStatus.innerHTML = '✅ 登录成功！Token已保存';
                    
                    // 获取用户信息
                    const userResponse = await fetch('http://localhost:8000/api/v1/auth/me', {
                        headers: {
                            'Authorization': 'Bearer ' + authToken,
                            'Accept': 'application/json'
                        }
                    });
                    
                    if (userResponse.ok) {
                        const userData = await userResponse.json();
                        const encryptedUser = btoa(encodeURIComponent(JSON.stringify(userData)));
                        sessionStorage.setItem('user_data', encryptedUser);
                    }
                    
                } else {
                    authStatus.className = 'status error';
                    authStatus.innerHTML = '❌ 登录失败: ' + (data.message || data.detail || '未知错误');
                }
                
            } catch (error) {
                authStatus.className = 'status error';
                authStatus.innerHTML = '❌ 登录请求失败: ' + error.message;
            }
        }
        
        // 测试API的通用函数
        async function testAPI(endpoint, description) {
            if (!authToken) {
                const isAuthenticated = await checkAuth();
                if (!isAuthenticated) {
                    return { error: '需要先登录' };
                }
            }
            
            try {
                const response = await fetch(`http://localhost:8000${endpoint}`, {
                    headers: {
                        'Authorization': 'Bearer ' + authToken,
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                return {
                    endpoint,
                    description,
                    status: response.status,
                    statusText: response.statusText,
                    success: response.ok,
                    data: data
                };
                
            } catch (error) {
                return {
                    endpoint,
                    description,
                    error: error.message
                };
            }
        }
        
        // 测试各个API
        async function testKPIAPI() {
            const result = await testAPI('/api/v1/monitoring/kpi?hours=24', 'KPI指标API');
            showResult(result);
        }
        
        async function testFlowStatusAPI() {
            const result = await testAPI('/api/v1/monitoring/flow-status', '流程状态API');
            showResult(result);
        }
        
        async function testRecentSignalsAPI() {
            const result = await testAPI('/api/v1/monitoring/recent-signals?limit=10', '最近信号API');
            showResult(result);
        }
        
        async function testActiveAlertsAPI() {
            const result = await testAPI('/api/v1/monitoring/alerts', '活跃告警API');
            showResult(result);
        }
        
        // 测试所有API
        async function testAllAPIs() {
            const apiStatus = document.getElementById('apiStatus');
            apiStatus.className = 'status info';
            apiStatus.innerHTML = '🔄 正在测试所有监控API...';
            
            const results = await Promise.all([
                testAPI('/api/v1/monitoring/kpi?hours=24', 'KPI指标API'),
                testAPI('/api/v1/monitoring/flow-status', '流程状态API'),
                testAPI('/api/v1/monitoring/recent-signals?limit=10', '最近信号API'),
                testAPI('/api/v1/monitoring/alerts', '活跃告警API')
            ]);
            
            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;
            
            if (successCount === totalCount) {
                apiStatus.className = 'status success';
                apiStatus.innerHTML = `✅ 所有API测试通过 (${successCount}/${totalCount})`;
            } else {
                apiStatus.className = 'status warning';
                apiStatus.innerHTML = `⚠️ 部分API测试失败 (${successCount}/${totalCount})`;
            }
            
            showResult({ allResults: results });
        }
        
        // 模拟前端数据流
        async function simulateFrontendDataFlow() {
            const dataFlowStatus = document.getElementById('dataFlowStatus');
            dataFlowStatus.className = 'status info';
            dataFlowStatus.innerHTML = '🔄 正在模拟前端数据流...';
            
            try {
                // 1. 检查认证
                const isAuthenticated = await checkAuth();
                if (!isAuthenticated) {
                    dataFlowStatus.className = 'status error';
                    dataFlowStatus.innerHTML = '❌ 认证失败，无法继续数据流测试';
                    return;
                }
                
                // 2. 模拟监控store的fetchAllMonitoringData方法
                const startTime = Date.now();
                
                const [kpiResult, flowResult, signalsResult, alertsResult] = await Promise.all([
                    testAPI('/api/v1/monitoring/kpi?hours=24', 'KPI指标'),
                    testAPI('/api/v1/monitoring/flow-status', '流程状态'),
                    testAPI('/api/v1/monitoring/recent-signals?limit=10', '最近信号'),
                    testAPI('/api/v1/monitoring/alerts', '活跃告警')
                ]);
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                // 3. 检查结果
                const allSuccess = [kpiResult, flowResult, signalsResult, alertsResult].every(r => r.success);
                
                if (allSuccess) {
                    dataFlowStatus.className = 'status success';
                    dataFlowStatus.innerHTML = `✅ 数据流测试成功，耗时: ${duration}ms`;
                    
                    // 模拟数据处理
                    const processedData = {
                        kpiMetrics: kpiResult.data?.data || {},
                        flowNodes: flowResult.data?.data?.nodes || [],
                        flowConnections: flowResult.data?.data?.connections || [],
                        recentSignals: signalsResult.data?.data || [],
                        activeAlerts: alertsResult.data?.data || [],
                        lastUpdated: new Date().toISOString(),
                        duration: duration
                    };
                    
                    showResult({ 
                        dataFlow: 'success',
                        processedData: processedData,
                        rawResults: [kpiResult, flowResult, signalsResult, alertsResult]
                    });
                    
                } else {
                    dataFlowStatus.className = 'status error';
                    dataFlowStatus.innerHTML = '❌ 数据流测试失败，部分API调用失败';
                    
                    showResult({ 
                        dataFlow: 'failed',
                        results: [kpiResult, flowResult, signalsResult, alertsResult]
                    });
                }
                
            } catch (error) {
                dataFlowStatus.className = 'status error';
                dataFlowStatus.innerHTML = '❌ 数据流测试异常: ' + error.message;
            }
        }
        
        // 测试缓存API调用
        async function testCachedAPI() {
            const dataFlowStatus = document.getElementById('dataFlowStatus');
            dataFlowStatus.className = 'status info';
            dataFlowStatus.innerHTML = '🔄 测试缓存API调用...';
            
            // 模拟前端的cachedApiCall函数
            const cache = new Map();
            
            async function cachedApiCall(cacheKey, apiCall) {
                if (cache.has(cacheKey)) {
                    return { cached: true, data: cache.get(cacheKey) };
                }
                
                const result = await apiCall();
                cache.set(cacheKey, result);
                return { cached: false, data: result };
            }
            
            try {
                // 第一次调用（应该从API获取）
                const result1 = await cachedApiCall('kpi-metrics-24', () => 
                    testAPI('/api/v1/monitoring/kpi?hours=24', 'KPI指标（首次）')
                );
                
                // 第二次调用（应该从缓存获取）
                const result2 = await cachedApiCall('kpi-metrics-24', () => 
                    testAPI('/api/v1/monitoring/kpi?hours=24', 'KPI指标（缓存）')
                );
                
                dataFlowStatus.className = 'status success';
                dataFlowStatus.innerHTML = '✅ 缓存API测试完成';
                
                showResult({
                    cacheTest: {
                        firstCall: result1,
                        secondCall: result2,
                        cacheWorking: result2.cached
                    }
                });
                
            } catch (error) {
                dataFlowStatus.className = 'status error';
                dataFlowStatus.innerHTML = '❌ 缓存API测试失败: ' + error.message;
            }
        }
        
        // 显示结果
        function showResult(result) {
            const resultsDiv = document.getElementById('results');
            const resultDetails = document.getElementById('resultDetails');
            
            resultDetails.textContent = JSON.stringify(result, null, 2);
            resultsDiv.style.display = 'block';
            
            // 滚动到结果区域
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 页面加载时自动检查认证状态
        window.onload = function() {
            checkAuth();
        };
    </script>
</body>
</html>
