<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端认证状态检查工具</h1>
        
        <div id="authStatus" class="status info">
            正在检查认证状态...
        </div>
        
        <div id="tokenInfo" class="status info" style="display: none;">
            <h3>Token信息</h3>
            <pre id="tokenDetails"></pre>
        </div>
        
        <div id="userInfo" class="status info" style="display: none;">
            <h3>用户信息</h3>
            <pre id="userDetails"></pre>
        </div>
        
        <div id="apiTest" class="status info" style="display: none;">
            <h3>API测试结果</h3>
            <pre id="apiResults"></pre>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="checkAuth()">重新检查认证状态</button>
            <button onclick="testLogin()">测试登录</button>
            <button onclick="testMonitoringAPI()">测试监控API</button>
            <button onclick="clearAuth()">清除认证信息</button>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>快速登录</h3>
            <p>如果您需要登录，请使用以下测试账户：</p>
            <ul>
                <li>用户名: <strong>demo</strong></li>
                <li>密码: <strong>password123</strong></li>
            </ul>
            <button onclick="window.open('http://localhost:5173/login', '_blank')">打开登录页面</button>
            <button onclick="window.open('http://localhost:5173/monitoring', '_blank')">打开监控页面</button>
        </div>
    </div>

    <script>
        // 检查认证状态
        function checkAuth() {
            const authStatus = document.getElementById('authStatus');
            const tokenInfo = document.getElementById('tokenInfo');
            const userInfo = document.getElementById('userInfo');
            
            try {
                // 检查sessionStorage中的token和用户信息
                const encryptedToken = sessionStorage.getItem('auth_token');
                const encryptedUser = sessionStorage.getItem('user_data');
                
                if (!encryptedToken) {
                    authStatus.className = 'status error';
                    authStatus.innerHTML = '❌ 未找到认证token，用户未登录';
                    tokenInfo.style.display = 'none';
                    userInfo.style.display = 'none';
                    return;
                }
                
                // 尝试解密token（简单的base64解码）
                let token = '';
                try {
                    token = decodeURIComponent(atob(encryptedToken));
                } catch (e) {
                    token = encryptedToken; // 如果解密失败，可能是未加密的
                }
                
                // 解析JWT token
                let tokenPayload = null;
                try {
                    const parts = token.split('.');
                    if (parts.length === 3) {
                        tokenPayload = JSON.parse(atob(parts[1]));
                    }
                } catch (e) {
                    console.error('Token解析失败:', e);
                }
                
                // 检查token是否过期
                const now = Math.floor(Date.now() / 1000);
                const isExpired = tokenPayload && tokenPayload.exp && tokenPayload.exp < now;
                
                if (isExpired) {
                    authStatus.className = 'status warning';
                    authStatus.innerHTML = '⚠️ Token已过期，需要重新登录';
                } else {
                    authStatus.className = 'status success';
                    authStatus.innerHTML = '✅ 用户已登录，认证状态正常';
                }
                
                // 显示token信息
                tokenInfo.style.display = 'block';
                document.getElementById('tokenDetails').textContent = JSON.stringify({
                    token: token.substring(0, 50) + '...',
                    payload: tokenPayload,
                    expired: isExpired,
                    expiresAt: tokenPayload?.exp ? new Date(tokenPayload.exp * 1000).toLocaleString() : 'Unknown'
                }, null, 2);
                
                // 显示用户信息
                if (encryptedUser) {
                    try {
                        let userData = decodeURIComponent(atob(encryptedUser));
                        const user = JSON.parse(userData);
                        userInfo.style.display = 'block';
                        document.getElementById('userDetails').textContent = JSON.stringify(user, null, 2);
                    } catch (e) {
                        console.error('用户数据解析失败:', e);
                    }
                }
                
            } catch (error) {
                authStatus.className = 'status error';
                authStatus.innerHTML = '❌ 认证状态检查失败: ' + error.message;
                console.error('认证检查错误:', error);
            }
        }
        
        // 测试登录
        async function testLogin() {
            try {
                const response = await fetch('http://localhost:8000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'username=demo&password=password123'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    alert('登录测试成功！Token: ' + data.access_token.substring(0, 50) + '...');
                } else {
                    alert('登录测试失败: ' + (data.message || data.detail || '未知错误'));
                }
            } catch (error) {
                alert('登录测试失败: ' + error.message);
            }
        }
        
        // 测试监控API
        async function testMonitoringAPI() {
            const apiTest = document.getElementById('apiTest');
            const apiResults = document.getElementById('apiResults');
            
            try {
                // 获取token
                const encryptedToken = sessionStorage.getItem('auth_token');
                if (!encryptedToken) {
                    apiResults.textContent = '错误: 未找到认证token，请先登录';
                    apiTest.style.display = 'block';
                    return;
                }
                
                let token = '';
                try {
                    token = decodeURIComponent(atob(encryptedToken));
                } catch (e) {
                    token = encryptedToken;
                }
                
                // 测试KPI API
                const response = await fetch('http://localhost:8000/api/v1/monitoring/kpi?hours=24', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                apiResults.textContent = JSON.stringify({
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, null, 2);
                
                apiTest.style.display = 'block';
                
            } catch (error) {
                apiResults.textContent = 'API测试失败: ' + error.message;
                apiTest.style.display = 'block';
            }
        }
        
        // 清除认证信息
        function clearAuth() {
            sessionStorage.removeItem('auth_token');
            sessionStorage.removeItem('user_data');
            alert('认证信息已清除');
            checkAuth();
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            checkAuth();
        };
    </script>
</body>
</html>
