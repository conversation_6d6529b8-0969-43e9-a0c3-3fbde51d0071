/**
 * Global Test Setup
 * Prepares the test environment before running tests
 */

import { chromium } from '@playwright/test'
import fs from 'fs'

async function globalSetup() {
  console.log('🚀 Starting global test setup...')
  console.log('📋 按照《0. 项目规范.md》检查服务依赖...')

  // 检测是否在Docker环境中
  const isDocker = process.env.DOCKER_ENV === 'true' || fs.existsSync('/.dockerenv')

  // 在Docker环境中使用系统安装的Chromium
  const launchOptions = isDocker ? {
    executablePath: '/usr/bin/chromium-browser',
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
  } : {}

  let browser, context, page

  try {
    // Check if PostgreSQL and backend are running
    console.log('🔍 Checking PostgreSQL and backend availability...')

    browser = await chromium.launch(launchOptions)
    context = await browser.newContext()
    page = await context.newPage()

    // 在Docker环境中使用容器名，否则使用localhost
    const backendUrl = process.env.API_BASE_URL ||
                      (isDocker ? 'http://backend-test:8000' : 'http://localhost:8000')

    console.log(`🔍 测试环境: ${isDocker ? 'Docker容器' : '本地开发'}`)
    console.log(`🔍 使用后端地址: ${backendUrl}`)

    try {
      const response = await page.goto(`${backendUrl}/api/v1/health`, {
        waitUntil: 'networkidle',
        timeout: 15000
      })

      if (response.ok()) {
        console.log('✅ Backend is available')

        // 检查数据库连接
        const healthData = await response.json()
        if (healthData && healthData.success && healthData.data && healthData.data.checks && healthData.data.checks.database === 'healthy') {
          console.log('✅ PostgreSQL database is connected')
        } else {
          console.log('⚠️ Backend is running but database connection status unknown')
        }
      } else {
        throw new Error(`Backend returned status: ${response.status()}`)
      }
    } catch (error) {
      console.error('❌ Backend or PostgreSQL not available:', error.message)
      console.error('💡 请按照《0. 项目规范.md》启动服务:')
      console.error('   🚀 推荐方式: docker-compose -f docker-compose.test.yml up -d')
      console.error('   📋 手动启动顺序:')
      console.error('   1. docker-compose -f docker-compose.test.yml up -d postgres-test')
      console.error('   2. docker-compose -f docker-compose.test.yml up -d backend-test')
      console.error('   3. docker-compose -f docker-compose.test.yml up -d frontend-test')
      console.error('')
      console.error('⚠️  注意: 项目已移除SQLite依赖，仅支持PostgreSQL')
      throw new Error('Backend/PostgreSQL is not running. E2E tests require both services.')
    }
    
    // Check if frontend is running
    console.log('🔍 Checking frontend availability...')
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173'
    
    try {
      const response = await page.goto(frontendUrl, {
        waitUntil: 'domcontentloaded',
        timeout: 30000
      })
      
      if (response.ok()) {
        console.log('✅ Frontend is available')
      } else {
        throw new Error(`Frontend returned status: ${response.status()}`)
      }
    } catch (error) {
      console.error('❌ Frontend not available:', error.message)
      throw new Error('Frontend is not running. Please start the test server.')
    }
    
    // Verify demo user exists
    console.log('🔍 Verifying demo user...')
    try {
      const loginResponse = await page.request.post(`${backendUrl}/api/v1/auth/login`, {
        data: {
          username: 'demo',
          password: 'password123'
        }
      })
      
      if (loginResponse.ok()) {
        console.log('✅ Demo user authentication successful')
      } else {
        console.warn('⚠️  Demo user authentication failed, creating user...')
        await createDemoUser(page, backendUrl)
      }
    } catch (error) {
      console.warn('⚠️  Could not verify demo user:', error.message)
    }
    
    // Create test data if needed
    console.log('🔍 Setting up test data...')
    await setupTestData(page, backendUrl)
    
    console.log('✅ Global setup completed successfully')
    
  } catch (error) {
    console.error('❌ Global setup failed:', error.message)
    throw error
  } finally {
    await context.close()
    await browser.close()
  }
}

async function createDemoUser(page, backendUrl) {
  try {
    const response = await page.request.post(`${backendUrl}/api/v1/auth/register`, {
      data: {
        username: 'demo',
        password: 'password123'
      }
    })
    
    if (response.ok()) {
      console.log('✅ Demo user created successfully')
    } else {
      const error = await response.json()
      if (error.detail?.includes('already exists') || error.message?.includes('已存在')) {
        console.log('✅ Demo user already exists')
      } else {
        console.warn('⚠️  Failed to create demo user:', error)
      }
    }
  } catch (error) {
    console.warn('⚠️  Error creating demo user:', error.message)
  }
}

async function setupTestData(page, backendUrl) {
  try {
    // Login as demo user to get token
    const loginResponse = await page.request.post(`${backendUrl}/api/v1/auth/login`, {
      data: {
        username: 'demo',
        password: 'password123'
      }
    })
    
    if (!loginResponse.ok()) {
      console.warn('⚠️  Could not login to setup test data')
      return
    }
    
    const loginData = await loginResponse.json()
    const token = loginData.access_token
    
    // Create some test orders if none exist
    const ordersResponse = await page.request.get(`${backendUrl}/api/v1/orders`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (ordersResponse.ok()) {
      const ordersData = await ordersResponse.json()
      if (ordersData.total === 0) {
        console.log('📝 Creating test orders...')
        await createTestOrders(page, backendUrl, token)
      } else {
        console.log(`✅ Found ${ordersData.total} existing orders`)
      }
    }
    
  } catch (error) {
    console.warn('⚠️  Could not setup test data:', error.message)
  }
}

async function createTestOrders(page, backendUrl, token) {
  const testOrders = [
    {
      symbol: 'BTC/USDT',
      side: 'buy',
      quantity: 0.001,
      entry_price: 50000.0
    },
    {
      symbol: 'ETH/USDT',
      side: 'sell',
      quantity: 0.1,
      entry_price: 3000.0
    }
  ]
  
  for (const order of testOrders) {
    try {
      const response = await page.request.post(`${backendUrl}/api/v1/orders`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        data: order
      })
      
      if (response.ok()) {
        console.log(`✅ Created test order: ${order.symbol}`)
      }
    } catch (error) {
      console.warn(`⚠️  Could not create test order ${order.symbol}:`, error.message)
    }
  }
}

export default globalSetup
